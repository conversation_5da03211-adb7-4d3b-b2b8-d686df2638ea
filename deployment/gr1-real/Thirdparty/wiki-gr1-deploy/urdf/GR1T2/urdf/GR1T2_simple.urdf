<robot name="GR1T2Simple">
    <link name="base">
        <inertial>
            <origin xyz="-0.048532 -0.00089322 -0.032851" rpy="0 0 0"/>
            <mass value="4.9859"/>
            <inertia ixx="0.045749" ixy="-0.00020848" ixz="-0.0025121" iyy="0.016772" iyz="-4.364E-05" izz="0.048814"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/base.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>

    <link name="l_thigh_roll">
        <inertial>
            <origin xyz="0.042108 5.3388E-05 0.0025561" rpy="0 0 0"/>
            <mass value="1.24083588"/>
            <inertia ixx="0.00098092" ixy="2E-08" ixz="6.191E-05" iyy="0.00109617" iyz="1.42E-06" izz="0.001388"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Lleg1.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="l_hip_roll" type="revolute">
        <origin xyz="-0.0480000000000005 0.105 -0.0575" rpy="0 0 0"/>
        <parent link="base"/>
        <child link="l_thigh_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.09" upper="0.79" effort="48" velocity="12.15"/>
    </joint>
    <link name="l_thigh_yaw">
        <inertial>
            <origin xyz="-0.00036975 0.048008 -0.10263" rpy="0 0 0"/>
            <mass value="3.9947"/>
            <inertia ixx="0.00974309" ixy="-5.137E-05" ixz="0.00011781" iyy="0.01400783" iyz="0.00112062" izz="0.00730946"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Lleg2.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="l_hip_yaw" type="revolute">
        <origin xyz="0.048 0 -0.0304999999999989" rpy="0 0 0"/>
        <parent link="l_thigh_roll"/>
        <child link="l_thigh_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-0.7" upper="0.7" effort="66" velocity="16.76"/>
    </joint>
    <link name="l_thigh_pitch">
        <inertial>
            <origin xyz="0.0023293 -0.018978 -0.09346" rpy="0 0 0"/>
            <mass value="6.1575"/>
            <inertia ixx="0.08138824" ixy="-0.00020343" ixz="0.00179871" iyy="0.08464794" iyz="0.00989289" izz="0.01559965"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Lleg3.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.15" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.35" radius="0.05"/>
            </geometry>
        </collision>
    </link>
    <joint name="l_hip_pitch" type="revolute">
        <origin xyz="0 0 -0.109999999999336" rpy="0 0 0"/>
        <parent link="l_thigh_yaw"/>
        <child link="l_thigh_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-1.75" upper="0.7" effort="225" velocity="37.38"/>
    </joint>
    <link name="l_shank_pitch">
        <inertial>
            <origin xyz="0.0026504 0.0058504 -0.12267" rpy="0 0 0"/>
            <mass value="2.4196"/>
            <inertia ixx="0.02094885" ixy="-1.793E-05" ixz="-0.00036658" iyy="0.02117876" iyz="0.00034981" izz="0.00160342"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Lleg4.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.15" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.2" radius="0.05"/>
            </geometry>
        </collision>
    </link>
    <joint name="l_knee_pitch" type="revolute">
        <origin xyz="0 0 -0.359995124115987" rpy="0 0 0"/>
        <parent link="l_thigh_pitch"/>
        <child link="l_shank_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.09" upper="1.92" effort="225" velocity="37.38"/>
    </joint>
    <link name="l_foot_pitch">
        <inertial>
            <origin xyz="-1.2374E-08 -1.3607E-08 2.2204E-16" rpy="0 0 0"/>
            <mass value="0.049737"/>
            <inertia ixx="5.2E-06" ixy="0" ixz="0" iyy="3.71E-06" iyz="0" izz="4.41E-06"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Lleg5.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="l_ankle_pitch" type="revolute">
        <origin xyz="0 0 -0.339999992262544" rpy="0 0 0"/>
        <parent link="l_shank_pitch"/>
        <child link="l_foot_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-1.05" upper="0.52" effort="15" velocity="20.32"/>
    </joint>
    <link name="l_foot_roll">
        <inertial>
            <origin xyz="0.035077 0.00026914 -0.036774" rpy="0 0 0"/>
            <mass value="0.59122118"/>
            <inertia ixx="0.00039846" ixy="2.9E-07" ixz="0.00013398" iyy="0.00258496" iyz="0" izz="0.00281514"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Lleg6.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision name="l_foot_1">
            <origin rpy="0 1.5708 0" xyz="0.05 0.02 -0.035"/>
            <geometry>
                <cylinder length="0.24" radius="0.02"/>
            </geometry>
        </collision>
        <collision name="l_foot_2">
            <origin rpy="0 1.5708 0" xyz="0.05 -0.02 -0.035"/>
            <geometry>
                <cylinder length="0.24" radius="0.02"/>
            </geometry>
        </collision>
    </link>
    <joint name="l_ankle_roll" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="l_foot_pitch"/>
        <child link="l_foot_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.44" upper="0.44" effort="30" velocity="20.32"/>
    </joint>

    <!-- ############################################################ -->

    <link name="r_thigh_roll">
        <inertial>
            <origin xyz="0.042109 -5.244E-05 0.0025563" rpy="0 0 0"/>
            <mass value="1.2408"/>
            <inertia ixx="0.00098097" ixy="-2E-08" ixz="6.189E-05" iyy="0.00109627" iyz="-1.43E-06" izz="0.00138804"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rleg1.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="r_hip_roll" type="revolute">
        <origin xyz="-0.0480000000000003 -0.105 -0.0575" rpy="0 0 0"/>
        <parent link="base"/>
        <child link="r_thigh_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.79" upper="0.09" effort="48" velocity="12.15"/>
    </joint>
    <link name="r_thigh_yaw">
        <inertial>
            <origin xyz="-0.000369 -0.048008 -0.10263" rpy="0 0 0"/>
            <mass value="3.9947"/>
            <inertia ixx="0.00974298" ixy="5.139E-05" ixz="0.0001179" iyy="0.01400765" iyz="-0.00112065" izz="0.0073094"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rleg2.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="r_hip_yaw" type="revolute">
        <origin xyz="0.0480000000000212 0 -0.0305000000000011" rpy="0 0 0"/>
        <parent link="r_thigh_roll"/>
        <child link="r_thigh_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-0.7" upper="0.7" effort="66" velocity="16.76"/>
    </joint>
    <link name="r_thigh_pitch">
        <inertial>
            <origin xyz="0.0023609 0.019072 -0.093254" rpy="0 0 0"/>
            <mass value="6.1575"/>
            <inertia ixx="0.08151107" ixy="0.00019413" ixz="0.0017867" iyy="0.08477204" iyz="-0.00994489" izz="0.01565212"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rleg3.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.15" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.35" radius="0.05"/>
            </geometry>
        </collision>
    </link>
    <joint name="r_hip_pitch" type="revolute">
        <origin xyz="0 0 -0.109999999999337" rpy="0 0 0"/>
        <parent link="r_thigh_yaw"/>
        <child link="r_thigh_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-1.75" upper="0.7" effort="225" velocity="37.38"/>
    </joint>
    <link name="r_shank_pitch">
        <inertial>
            <origin xyz="0.0026636 -0.0058849 -0.12251" rpy="0 0 0"/>
            <mass value="2.4196"/>
            <inertia ixx="0.02088663" ixy="1.78E-05" ixz="-0.00036399" iyy="0.02111895" iyz="-0.00036161" izz="0.00160131"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rleg4.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.15" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.2" radius="0.05"/>
            </geometry>
        </collision>
    </link>
    <joint name="r_knee_pitch" type="revolute">
        <origin xyz="0 0 -0.359995124115985" rpy="0 0 0"/>
        <parent link="r_thigh_pitch"/>
        <child link="r_shank_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.09" upper="1.92" effort="225" velocity="37.38"/>
    </joint>
    <link name="r_foot_pitch">
        <inertial>
            <origin xyz="-1.2374E-08 1.3607E-08 -2.1195E-10" rpy="0 0 0"/>
            <mass value="0.049737"/>
            <inertia ixx="5.2E-06" ixy="0" ixz="0" iyy="3.71E-06" iyz="0" izz="4.41E-06"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rleg5.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="r_ankle_pitch" type="revolute">
        <origin xyz="0 0 -0.339999999788054" rpy="0 0 0"/>
        <parent link="r_shank_pitch"/>
        <child link="r_foot_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-1.05" upper="0.52" effort="15" velocity="20.32"/>
    </joint>
    <link name="r_foot_roll">
        <inertial>
            <origin xyz="0.035078 -0.00026903 -0.036775" rpy="0 0 0"/>
            <mass value="0.59122"/>
            <inertia ixx="0.00039843" ixy="-3.3E-07" ixz="0.00013399" iyy="0.00258489" iyz="0" izz="0.00281503"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rleg6.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision name="r_foot_1">
            <origin rpy="0 1.5708 0" xyz="0.05 0.02 -0.035"/>
            <geometry>
                <cylinder length="0.24" radius="0.02"/>
            </geometry>
        </collision>
        <collision name="r_foot_2">
            <origin rpy="0 1.5708 0" xyz="0.05 -0.02 -0.035"/>
            <geometry>
                <cylinder length="0.24" radius="0.02"/>
            </geometry>
        </collision>
    </link>
    <joint name="r_ankle_roll" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="r_foot_pitch"/>
        <child link="r_foot_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.44" upper="0.44" effort="30" velocity="20.32"/>
    </joint>

    <!-- ############################################################ -->

    <link name="waist_yaw">
        <inertial>
            <origin xyz="-0.0049526 -0.00090609 0.0262" rpy="0 0 0"/>
            <mass value="0.50743792"/>
            <inertia ixx="0.00110565" ixy="-4.9E-07" ixz="4.837E-05" iyy="0.00082433" iyz="1.198E-05" izz="0.00141909"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/waist3.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="waist_yaw" type="fixed">
        <origin xyz="0 0 0.00649999999999984" rpy="0 0 0"/>
        <parent link="base"/>
        <child link="waist_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-1.05" upper="1.05" effort="66" velocity="16.76"/>
    </joint>
    <link name="waist_pitch">
        <inertial>
            <origin xyz="0.0045612 -0.0044005 0.040369" rpy="0 0 0"/>
            <mass value="2.90421452"/>
            <inertia ixx="0.00845426" ixy="-5.355E-05" ixz="-0.00049265" iyy="0.00844076" iyz="-0.00047579" izz="0.00329151"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/waist2.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="waist_pitch" type="fixed">
        <origin xyz="0 0 0.0570000000000001" rpy="0 0 0"/>
        <parent link="waist_yaw"/>
        <child link="waist_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.52" upper="1.22" effort="66" velocity="16.76"/>
    </joint>
    <link name="waist_roll">
        <inertial>
            <origin xyz="-0.0094995 8.7428E-05 0.16729" rpy="0 0 0"/>
            <mass value="7.54469333"/>
            <inertia ixx="0.06293498" ixy="5.308E-05" ixz="0.00175668" iyy="0.05101438" iyz="-3.98E-06" izz="0.03853047"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/waist1.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0.15" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.2" radius="0.1"/>
            </geometry>
        </collision>
    </link>
    <joint name="waist_roll" type="fixed">
        <origin xyz="0 0 0.081" rpy="0 0 0"/>
        <parent link="waist_pitch"/>
        <child link="waist_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.7" upper="0.7" effort="66" velocity="16.76"/>
    </joint>

    <!-- ############################################################ -->

    <link name="torso">
    </link>
    <joint name="joint_torso" type="fixed">
        <origin xyz="0 0 0.160213351037685" rpy="0 0 0"/>
        <parent link="waist_roll"/>
        <child link="torso"/>
        <axis xyz="0 0 0"/>
    </joint>

    <!-- ############################################################ -->

    <link name="head_pitch">
        <inertial>
            <origin xyz="0.00043792 1.5503E-08 -1.1298E-08" rpy="0 0 0"/>
            <mass value="0.00543512"/>
            <inertia ixx="3.6E-07" ixy="0" ixz="0" iyy="7E-08" iyz="0" izz="3.6E-07"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/head3.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="joint_head_pitch" type="fixed">
        <origin xyz="0.000999999999997045 -0.000225512658326668 0.144786985472057" rpy="0 0 0"/>
        <parent link="torso"/>
        <child link="head_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.87" upper="0.87" effort="3.95" velocity="27.96"/>
    </joint>
    <link name="head_roll">
        <inertial>
            <origin xyz="-0.003976 2.2544E-05 0.015347" rpy="0 0 0"/>
            <mass value="0.07131711"/>
            <inertia ixx="2.863E-05" ixy="0" ixz="-2.8E-06" iyy="2.039E-05" iyz="0" izz="2.713E-05"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/head2.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="joint_head_roll" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="head_pitch"/>
        <child link="head_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.35" upper="0.35" effort="3.95" velocity="27.96"/>
    </joint>
    <link name="head_yaw">
        <inertial>
            <origin xyz="0.02323 0.00026918 0.11024" rpy="0 0 0"/>
            <mass value="1.3898352"/>
            <inertia ixx="0.00803907" ixy="-9.9E-07" ixz="0.00082206" iyy="0.00988205" iyz="-1.27E-06" izz="0.00625075"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/head1.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="joint_head_yaw" type="fixed">
        <origin xyz="0 5.37300128340204E-05 0.0359999599039489" rpy="0 0 0"/>
        <parent link="head_roll"/>
        <child link="head_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-2.71" upper="2.71" effort="3.95" velocity="27.96"/>
    </joint>

    <!-- ############################################################ -->

    <link name="l_upper_arm_pitch">
        <inertial>
            <origin xyz="0.0054188 0.05902 0.00011843" rpy="0 0 0"/>
            <mass value="0.78809"/>
            <inertia ixx="0.0007753" ixy="-2.589E-05" ixz="3.7E-07" iyy="0.00049478" iyz="7.2E-06" izz="0.00067965"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Larm1.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="l_shoulder_pitch" type="fixed">
        <origin xyz="0 0.126623540943386 0.0590455268445764" rpy="0.436334785573203 0 0"/>
        <parent link="torso"/>
        <child link="l_upper_arm_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.79" upper="1.92" effort="38" velocity="9.11"/>
    </joint>
    <link name="l_upper_arm_roll">
        <inertial>
            <origin xyz="0.01986 0.02608 -0.025535" rpy="0 0 0"/>
            <mass value="0.1786"/>
            <inertia ixx="0.000202" ixy="3.959E-05" ixz="-4.406E-05" iyy="0.00016117" iyz="4.295E-05" izz="0.00017433"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Larm2.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1"/>
            </material>
        </visual>
    </link>
    <joint name="l_shoulder_roll" type="fixed">
        <origin xyz="0 0.067 0"
                rpy="-0.2364 0 0"/>
        <parent link="l_upper_arm_pitch"/>
        <child link="l_upper_arm_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.57" upper="3.27" effort="38" velocity="9.11"/>
    </joint>
    <link name="l_upper_arm_yaw">
        <inertial>
            <origin xyz="0.00010029 0.0012809 -0.097194" rpy="0 0 0"/>
            <mass value="0.98085"/>
            <inertia ixx="0.00618618" ixy="-1.2E-07" ixz="1.28E-06" iyy="0.00611274" iyz="0.00019627" izz="0.00057714"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Larm3.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.05" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.11" radius="0.03"/>
            </geometry>

        </collision>
    </link>
    <joint name="l_shoulder_yaw" type="fixed">
        <origin xyz="0 0.040381430445538 -0.0572327709897897" rpy="0 0 0"/>
        <parent link="l_upper_arm_roll"/>
        <child link="l_upper_arm_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-2.97" upper="2.97" effort="30" velocity="7.33"/>
    </joint>
    <link name="l_lower_arm_pitch">
        <inertial>
            <origin xyz="7.6884E-09 0.020584 -0.020113" rpy="0 0 0"/>
            <mass value="0.059535"/>
            <inertia ixx="2.796E-05" ixy="0" ixz="0" iyy="2.596E-05" iyz="-7.95E-06" izz="1.931E-05"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Larm4.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="l_elbow_pitch" type="fixed">
        <origin xyz="0 0.00125501157055688 -0.188749174791823"
                rpy="0 -0.3 0"/>
        <parent link="l_upper_arm_yaw"/>
        <child link="l_lower_arm_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.27" upper="2.27" effort="30" velocity="7.33"/>
    </joint>
    <link name="l_hand_yaw">
        <inertial>
            <origin xyz="-0.00048885 0.0013916 -0.08204" rpy="0 0 0"/>
            <mass value="1.2684"/>
            <inertia ixx="0.00419457" ixy="6.15E-06" ixz="5E-07" iyy="0.00409118" iyz="-1.263E-05" izz="0.00084731"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Larm5.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.08" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.17" radius="0.035"/>
            </geometry>
        </collision>
    </link>
    <joint name="l_wrist_yaw" type="fixed">
        <origin xyz="0 0.000299612107771352 -0.0404989031042769" rpy="0 0 0"/>
        <parent link="l_lower_arm_pitch"/>
        <child link="l_hand_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-2.97" upper="2.97" effort="10.2" velocity="24.4"/>
    </joint>
    <link name="l_hand_roll">
        <inertial>
            <origin xyz="1.566E-08 -0.00043791 -2.922E-06" rpy="0 0 0"/>
            <mass value="0.0054351"/>
            <inertia ixx="7E-08" ixy="0" ixz="0" iyy="3.6E-07" iyz="0" izz="3.6E-07"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Larm6.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="l_wrist_roll" type="fixed">
        <origin xyz="0 0.00133699944291396 -0.20107985186864" rpy="0 0 0"/>
        <parent link="l_hand_yaw"/>
        <child link="l_hand_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.96" upper="0.87" effort="3.95" velocity="27.96"/>
    </joint>
    <link name="l_hand_pitch">
        <inertial>
            <origin xyz="0.0058876 -0.0010755 -0.082238" rpy="0 0 0"/>
            <mass value="0.54891"/>
            <inertia ixx="0.0011317" ixy="0.00013711" ixz="-2.516E-05" iyy="0.0014781" iyz="4.232E-05" izz="0.00058665"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Larm7.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.07" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.036"/>
            </geometry>
        </collision>
    </link>
    <joint name="l_wrist_pitch" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="l_hand_roll"/>
        <child link="l_hand_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.61" upper="0.61" effort="3.95" velocity="27.96"/>
    </joint>
    <link name="r_upper_arm_pitch">
        <inertial>
            <origin xyz="0.0054189 -0.058978 0.0001189" rpy="0 0 0"/>
            <mass value="0.78809"/>
            <inertia ixx="0.00077478" ixy="2.506E-05" ixz="3.6E-07" iyy="0.00049477" iyz="-9.49E-06" izz="0.00067913"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rarm1.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="r_shoulder_pitch" type="fixed">
        <origin xyz="0 -0.126623540926852 0.0590455268368039" rpy="-0.436332306093773 0 0"/>
        <parent link="torso"/>
        <child link="r_upper_arm_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.79" upper="1.92" effort="38" velocity="9.11"/>
    </joint>
    <link name="r_upper_arm_roll">
        <inertial>
            <origin xyz="0.019868 -0.025881 -0.025684" rpy="0 0 0"/>
            <mass value="0.1786"/>
            <inertia ixx="0.00020199" ixy="-3.928E-05" ixz="-4.432E-05" iyy="0.00016173" iyz="-4.302E-05" izz="0.00017374"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rarm2.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1"/>
            </material>
        </visual>
    </link>
    <joint name="r_shoulder_roll" type="fixed">
        <origin xyz="0 -0.067 0"
                rpy="0.23627 0 0"/>
        <parent link="r_upper_arm_pitch"/>
        <child link="r_upper_arm_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-3.27" upper="0.57" effort="38" velocity="9.11"/>
    </joint>
    <link name="r_upper_arm_yaw">
        <inertial>
            <origin xyz="-0.00010032 -0.00063501 -0.097127" rpy="0 0 0"/>
            <mass value="0.98085"/>
            <inertia ixx="0.0061732" ixy="1E-08" ixz="-1.2E-06" iyy="0.00610224" iyz="-0.00015934" izz="0.00057482"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rarm3.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.05" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.11" radius="0.03"/>
            </geometry>
        </collision>
    </link>
    <joint name="r_shoulder_yaw" type="fixed">
        <origin xyz="0 -0.039999999998954 -0.0575000000000302" rpy="0 0 0"/>
        <parent link="r_upper_arm_roll"/>
        <child link="r_upper_arm_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-2.97" upper="2.97" effort="30" velocity="7.33"/>
    </joint>
    <link name="r_lower_arm_pitch">
        <inertial>
            <origin xyz="-7.6455E-09 -0.020449 -0.020249" rpy="0 0 0"/>
            <mass value="0.059535"/>
            <inertia ixx="2.796E-05" ixy="0" ixz="0" iyy="2.586E-05" iyz="8E-06" izz="1.942E-05"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rarm4.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.79216 0.81961 0.93333 1"/>
            </material>
        </visual>
    </link>
    <joint name="r_elbow_pitch" type="fixed">
        <origin xyz="0 0 -0.188530330300762"
                rpy="0 -0.3 0"/>
        <parent link="r_upper_arm_yaw"/>
        <child link="r_lower_arm_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-2.27" upper="2.27" effort="30" velocity="7.33"/>
    </joint>
    <link name="r_hand_yaw">
        <inertial>
            <origin xyz="0.00048881 -0.00084627 -0.082047" rpy="0 0 0"/>
            <mass value="1.2684"/>
            <inertia ixx="0.00419457" ixy="6.15E-06" ixz="-4.6E-07" iyy="0.00409087" iyz="3.42E-05" izz="0.00084762"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rarm5.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.08" rpy="0 0 0"/>
            <geometry>
                <cylinder length="0.17" radius="0.035"/>
            </geometry>
        </collision>
    </link>
    <joint name="r_wrist_yaw" type="fixed">
        <origin xyz="0 -3.03303104252084E-05 -0.0404999999922469" rpy="0 0 0"/>
        <parent link="r_lower_arm_pitch"/>
        <child link="r_hand_yaw"/>
        <axis xyz="0 0 1"/>
        <limit lower="-2.97" upper="2.97" effort="10.2" velocity="24.4"/>
    </joint>
    <link name="r_hand_roll">
        <inertial>
            <origin xyz="-1.552E-08 0.00043792 -1.1275E-08" rpy="0 0 0"/>
            <mass value="0.0054351"/>
            <inertia ixx="7E-08" ixy="0" ixz="0" iyy="3.6E-07" iyz="0" izz="3.6E-07"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rarm6.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.8902 0.8902 0.91373 1"/>
            </material>
        </visual>
    </link>
    <joint name="r_wrist_roll" type="fixed">
        <origin xyz="0 0 -0.201084296746609" rpy="0 0 0"/>
        <parent link="r_hand_yaw"/>
        <child link="r_hand_roll"/>
        <axis xyz="1 0 0"/>
        <limit lower="-0.87" upper="0.96" effort="3.95" velocity="27.96"/>
    </joint>
    <link name="r_hand_pitch">
        <inertial>
            <origin xyz="0.0058875 0.0016218 -0.082229" rpy="0 0 0"/>
            <mass value="0.54891"/>
            <inertia ixx="0.00113168" ixy="-0.00013694" ixz="-2.607E-05" iyy="0.00147867" iyz="-3.638E-05" izz="0.00058614"/>
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0"/>
            <geometry>
                <mesh filename="../meshes/Rarm7.STL"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 -0.07" rpy="0 0 0"/>
            <geometry>
                <sphere radius="0.036"/>
            </geometry>
        </collision>
    </link>
    <joint name="r_wrist_pitch" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <parent link="r_hand_roll"/>
        <child link="r_hand_pitch"/>
        <axis xyz="0 1 0"/>
        <limit lower="-0.61" upper="0.61" effort="3.95" velocity="27.96"/>
    </joint>

    <!-- ############################################################ -->

    <link name="z_link_imu">
        <visual>
            <geometry>
                <box size="0.05 0.05 0.02"/>
            </geometry>
            <material name="">
                <color rgba="0.75294 0.75294 0.75294 1"/>
            </material>
        </visual>
    </link>
    <joint name="z_joint_imu" type="fixed">
        <origin xyz="-0.0645 0 -0.102" rpy="0 0 0"/>
        <parent link="base"/>
        <child link="z_link_imu"/>
    </joint>

    <!-- ############################################################ -->

</robot>