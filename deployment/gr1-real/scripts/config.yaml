device_connected: true
cpu: "X64"
system: "LINUX"
robot:
  name: "GR1"
  mechanism: "T2"
  control_period: 0.01
  input_and_calculate_period: 0

comm:
  enable: false
  use_raw: false
  use_json: false
  use_dds: false
  use_sim: false

operator:
  use_joystick: false #true
  joystick_connected: false #true
  joystick_type: "XBOX"
  use_keyboard: false
  keyboard_connected: false

ota:
  enable: false

hardware:
  use_can: false
  use_ethernet: false
  use_etherbus: false
  use_fi_fse: true
  use_fi_fsa: true

sensor_usb_imu:
  usb: [
    "/dev/ttyUSB0"
  ]
  angle_direction: [
    [ 1, 1, 1 ]
  ]
  angular_velocity_direction: [
    [ 1, 1, 1 ]
  ]
  comm_enable: [
    true
  ]
  comm_frequency: [
    500
  ]

sensor_abs_encoder:
  type: "FIFSEV1"
  ip: [
    # left leg
    "***************", "***************", "***************", "***************", "***************", "***************",
    # right leg
    "***************", "***************", "***************", "***************", "***************", "***************",
    # waist
    "***************", "***************", "***************",
  ]
  data_path: "sensor_offset.json"

fi_fse:
  version: "v1"

fi_fsa:
  version: "v1"
  concurrency: false

actuator:
  type: "FIFSAV1"
  ip: [
    # left leg
    "**************", "**************", "**************", "**************", "**************", "**************",
    # right leg
    "**************", "**************", "**************", "**************", "**************", "**************",
    # waist
    "**************", "**************", "**************",
    # head
    "**************", "**************", "**************",
    # left arm
    "**************" , "**************" , "**************" , "**************" , "**************" , "**************" , "**************",
    # right arm
    "**************" , "**************" , "**************" , "**************" , "**************" , "**************" , "**************",
  ]
  comm_enable: [
    # left leg
    true, true, true, true, true, true,
    # right leg
    true, true, true, true, true, true,
    # waist
    true, true, true,
    # head
    true, true, true,
    # left arm
    true, true, true, true, true, true, true,
    # right arm
    true, true, true, true, true, true, true,
  ]
  comm_block: [
    # left leg
    false, false, false, false, false, false,
    # right leg
    false, false, false, false, false, false,
    # waist
    false, false, false,
    # head
    false, false, false,
    # left arm
    false, false, false, false, false, false, false,
    # right arm
    false, false, false, false, false, false, false,
  ]
  comm_use_fast: [
    # left leg
    true, true, true, true, true, true,
    # right leg
    true, true, true, true, true, true,
    # waist
    true, true, true,
    # head
    true, true, true,
    # left arm
    true, true, true, true, true, true, true,
    # right arm
    true, true, true, true, true, true, true,
  ]
