import torch
import torch.nn as nn
from rsl_rl.modules.actor_critic import Actor, get_activation
from torch.distributions import Normal


class McpActorCritic(nn.Module):
    is_recurrent = False
    def __init__(self, num_prop,
                 num_critic_obs,
                 num_priv_latent, 
                 num_hist,
                 num_actions,
                 num_prims,
                 actor_hidden_dims,
                 critic_hidden_dims,
                 composer_hidden_dims,
                 activation="elu", 
                 init_noise_std=1.0,
                 fix_action_std=False,
                 ensemble_type="mcp",
                 num_task_obs=None,
                 **kwargs) -> None:
        super().__init__()
        self.fix_action_std = fix_action_std
        self.kwargs = kwargs
        activation = get_activation(activation)

        if self.kwargs.get("teacher_policy", False):
            num_prop = num_critic_obs
        self.num_proprio = num_prop
        self.num_composer_input = num_prop
        self.num_actions = num_actions
        self.num_prims = num_prims
        self.ensemble_type = ensemble_type

        # construct specified num of primative actors
        self.actors = nn.ModuleList()
        for i in range(num_prims):
            self.actors.append(Actor(self.num_proprio, 2*num_actions, actor_hidden_dims,
                                      activation, kwargs["tanh_encoder_output"]))
            
        # construct the critic
        critic_layers = []
        critic_layers.append(nn.Linear(num_critic_obs, critic_hidden_dims[0]))
        critic_layers.append(activation)
        for l in range(len(critic_hidden_dims)):
            if l == len(critic_hidden_dims) - 1:
                critic_layers.append(nn.Linear(critic_hidden_dims[l], 1))
            else:
                critic_layers.append(nn.Linear(critic_hidden_dims[l], critic_hidden_dims[l + 1]))
                critic_layers.append(activation)
        self.critic = nn.Sequential(*critic_layers)
        
        # construct the composer
        composer_layers = [nn.Linear(self.num_composer_input, composer_hidden_dims[0], )]
        composer_layers.append(activation)
        for l in range(len(composer_hidden_dims)):
            if l == len(composer_hidden_dims) - 1:
                composer_layers.append(nn.Linear(composer_hidden_dims[l], num_prims))
            else:
                composer_layers.append(nn.Linear(composer_hidden_dims[l], composer_hidden_dims[l + 1]))
                composer_layers.append(activation)
        self.composer = nn.Sequential(*composer_layers)

        # Action noise
        if self.fix_action_std:
            action_std = self.kwargs.get("action_std", None)
            assert action_std is not None, "action_std must be provided"
            action_std = torch.tensor(action_std)
            self.std = nn.Parameter(action_std, requires_grad=False)
        else:
            self.std = nn.Parameter(init_noise_std * torch.ones(num_actions))
        self.distribution = None
        # disable args validation for speedup
        Normal.set_default_validate_args = False

    def forward_weights(self, obs_all):
        weights = self.composer(obs_all[:, :self.num_composer_input])
        if self.ensemble_type == "discrete_moe":
            max_idx = weights.argmax(dim=-1, keepdim=True)
            weights = torch.zeros_like(weights).scatter_(dim=-1, index=max_idx, value=1.0)
        elif self.ensemble_type == "addictive_moe":
            weights = torch.nn.functional.softmax(weights, dim=-1)
        elif self.ensemble_type == "mcp":
            weights = torch.nn.functional.sigmoid(weights)
        else:
            raise NotImplementedError
        return weights

    def forward_primitive(self, idx, state, eval_mode=False):
        out = self.actors[idx](state, eval=eval_mode)
        mu, log_std = out.chunk(2, dim=-1)
        std = torch.ones_like(mu) * log_std.exp()
        return mu, std
    
    # derive the distribution composed from primitives 
    def update_distribution(self, obs_all):
        outs = [self.forward_primitive(i, obs_all[:, :self.num_proprio]) for i in range(self.num_prims)]
        mus, sigmas = zip(*outs)
        mus, sigmas = torch.stack(mus, dim=1), torch.stack(sigmas, dim=1)

        weights = self.forward_weights(obs_all)
        weights = weights[..., None]  # expand the feature dim

        zeta = (weights / sigmas).sum(dim=1)
        unnorm_mus = (weights * mus / sigmas).sum(dim=1)
        mean = unnorm_mus / zeta
        if self.fix_action_std:
            std = 1 / zeta
        else:
            std = self.std
        self.distribution = Normal(mean, mean*0 + std)

    def act(self, obs_all, **kwargs):
        self.update_distribution(obs_all)
        return self.distribution.sample()
    
    def get_actions_log_prob(self, actions):
        return self.distribution.log_prob(actions).sum(dim=-1)
    
    def act_inference(self, observations, eval=False, **kwargs):
        if not eval:
            outs = [self.forward_primitive(i, observations) for i in range(self.num_prims)]
        else:
            outs = [self.forward_primitive(i, observations, eval=True) for i in range(self.num_prims)]
        mus, sigmas = zip(*outs)
        mus, sigmas = torch.stack(mus, dim=1), torch.stack(sigmas, dim=1)
        weights = self.forward_weights(observations)
        weights = weights[..., None]  # expand the feature dim
        
        zeta = (weights / sigmas).sum(dim=1)
        unnorm_mus = (weights * mus / sigmas).sum(dim=1)
        mean = unnorm_mus / zeta
        return mean
    
    def evaluate(self, critic_observations, **kwargs):
        value = self.critic(critic_observations)
        return value
    
    def reset_std(self, std, num_actions, device):
        new_std = std * torch.ones(num_actions, device=device)
        self.std.data = new_std.data
    
    def reset(self, dones=None):
        pass

    # freeze the pretrained actors if had been loaded
    def freeze_primitives(self, idx):
        for param in self.actors[:idx].parameters():
            param.requires_grad = False

    # extract the actor state dict from the saved model
    def load_base_nets(self, state_dicts, num_prims=1):
        for i in range(num_prims):
            self.actors[i].load_state_dict(state_dicts[i])

    @property
    def action_mean(self):
        return self.distribution.mean

    @property
    def action_std(self):
        return self.distribution.stddev
    
    @property
    def entropy(self):
        return self.distribution.entropy().sum(dim=-1)
    