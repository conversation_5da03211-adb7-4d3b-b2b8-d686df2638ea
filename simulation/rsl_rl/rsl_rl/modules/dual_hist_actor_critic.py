import torch
import torch.nn as nn
from torch.distributions import Normal
from rsl_rl.modules.actor_critic import ActorCritic, get_activation
from rsl_rl.modules.actor_critic_rma import ActorCriticRMA


class LongHistoryEncoder(nn.Module):
    def __init__(self, activation_fn, input_size, tsteps, output_size, tanh_encoder_output=False):
        # self.device = device
        super(LongHistoryEncoder, self).__init__()
        self.activation_fn = activation_fn
        self.tsteps = tsteps

        channel_size = 10
        self.dropout_rate = 0.2

        self.encoder = nn.Sequential(
                nn.Linear(input_size, 3 * channel_size), self.activation_fn,
                )

        if tsteps == 50:
            self.conv_layers = nn.Sequential(
                    nn.Conv1d(in_channels = 3 * channel_size, out_channels = 2 * channel_size, kernel_size = 8, stride = 4), self.activation_fn,
                    nn.Conv1d(in_channels = 2 * channel_size, out_channels = channel_size, kernel_size = 5, stride = 1), self.activation_fn,
                    nn.Conv1d(in_channels = channel_size, out_channels = channel_size, kernel_size = 5, stride = 1), self.activation_fn, nn.Flatten())
        elif tsteps == 10:
            self.conv_layers = nn.Sequential(
                nn.Conv1d(in_channels = 3 * channel_size, out_channels = 2 * channel_size, kernel_size = 4, stride = 2), self.activation_fn,
                nn.Conv1d(in_channels = 2 * channel_size, out_channels = channel_size, kernel_size = 2, stride = 1), self.activation_fn,
                nn.Flatten())
        elif tsteps == 20:
            self.conv_layers = nn.Sequential(
                nn.Conv1d(in_channels = 3 * channel_size, out_channels = 2 * channel_size, kernel_size = 6, stride = 2), self.activation_fn,
                nn.Dropout(p=self.dropout_rate),
                nn.Conv1d(in_channels = 2 * channel_size, out_channels = channel_size, kernel_size = 4, stride = 2), self.activation_fn,
                nn.Dropout(p=self.dropout_rate),
                nn.Flatten())
        else:
            raise(ValueError("tsteps must be 10, 20 or 50"))

        self.linear_output = nn.Sequential(
                nn.Linear(channel_size * 3, output_size), self.activation_fn
                )

    def forward(self, obs):
        nd = obs.shape[0]
        T = self.tsteps
        projection = self.encoder(obs.reshape([nd * T, -1])) # do projection for n_proprio -> 32
        output = self.conv_layers(projection.reshape([nd, T, -1]).permute((0, 2, 1)))
        output = self.linear_output(output)
        return output


class DualHistActor(nn.Module):
    def __init__(self,
                 num_prop,
                 num_actions,
                 actor_hidden_dims,
                 activation,
                 short_hist_len,
                 long_hist_len,
                 num_task_obs,
                 num_priv_explicit,
                 num_priv_latent,
                 short_hist_embed_dim,
                 tanh_encoder_output=False
                 ):
        super().__init__()
        self.num_prop = num_prop  # including the proprio_obs and the task_obs
        self.num_task = num_task_obs
        self.num_priv_explicit = num_priv_explicit
        self.num_priv_latent = num_priv_latent
        self.num_actions = num_actions
        self.short_hist_len = short_hist_len
        self.short_hist_embed_dim = short_hist_embed_dim
        self.long_hist_len = long_hist_len

        # construct the short hist embedder
        short_hist_encoder = []
        short_hist_encoder.append(nn.Linear(self.num_prop, 128))
        short_hist_encoder.append(activation)
        short_hist_encoder.append(nn.Linear(128, short_hist_embed_dim))
        short_hist_encoder.append(activation)
        self.short_hist_encoder = nn.Sequential(*short_hist_encoder)
        
        short_hist_merger = []
        short_hist_merger.append(nn.Linear(short_hist_embed_dim*short_hist_len, short_hist_embed_dim))
        short_hist_merger.append(activation)
        self.short_hist_merger = nn.Sequential(*short_hist_merger)

        long_hist_latent_dim = short_hist_embed_dim * 2
        # construct the long hist temporal encoder (TCN)
        self.long_hist_encoder = LongHistoryEncoder(
            activation, num_prop, long_hist_len, long_hist_latent_dim,
        )

        backbone_input_dim = num_prop + num_task_obs + short_hist_embed_dim + long_hist_latent_dim
        actor_layers = []
        actor_layers.append(nn.Linear(backbone_input_dim, 
                                      actor_hidden_dims[0]))
        actor_layers.append(activation)
        for l in range(len(actor_hidden_dims)):
            if l == len(actor_hidden_dims) - 1:
                actor_layers.append(nn.Linear(actor_hidden_dims[l], num_actions))
            else:
                actor_layers.append(nn.Linear(actor_hidden_dims[l], actor_hidden_dims[l + 1]))
                actor_layers.append(activation)
        if tanh_encoder_output:
            actor_layers.append(nn.Tanh())
        self.actor_backbone = nn.Sequential(*actor_layers)

    def forward(self, obs_all, hist_encoding=False, eval=False, scandots_latent=None):
        obs = obs_all
        obs_prop = obs[:, :self.num_prop]
        obs_task = obs[:, self.num_prop : self.num_prop + self.num_task]
        obs_short_hist = obs[:, -self.short_hist_len*self.num_prop:].clone()
        obs_long_hist = obs[:, -self.long_hist_len*self.num_prop:]
        short_hist_latent = self.short_hist_encoder(obs_short_hist.reshape(-1, self.num_prop))
        short_hist_latent = self.short_hist_merger(short_hist_latent.view(obs_all.shape[0], -1))
        long_hist_latent = self.long_hist_encoder(obs_long_hist)
        backbone_input = torch.cat([obs_prop, obs_task, short_hist_latent, long_hist_latent], dim=1)
        backbone_output = self.actor_backbone(backbone_input)
        return backbone_output


class DualHistActorCritic(nn.Module):
    is_recurrent = False
    def __init__(self,  num_prop,
                        num_critic_obs,
                        short_hist_len,
                        long_hist_len,
                        num_actions,
                        actor_hidden_dims=[256, 256, 256],
                        critic_hidden_dims=[256, 256, 256],
                        activation='elu',
                        init_noise_std=1.0,
                        num_task_obs=None,
                        fix_action_std=False,
                        num_priv_explicit=None,
                        num_priv_latent=None, 
                        **kwargs):
        if kwargs:
            print("ActorCritic.__init__ got unexpected arguments, which will be ignored: " + str([key for key in kwargs.keys()]))
        super().__init__()
        self.fix_action_std = fix_action_std
        self.num_prop = num_prop
        self.num_critic_obs = num_critic_obs

        self.kwargs = kwargs
        activation = get_activation(activation)

        self.actor = DualHistActor(num_prop, num_actions, actor_hidden_dims, activation, short_hist_len, 
                                long_hist_len, num_task_obs, num_priv_explicit,
                                num_priv_latent, short_hist_embed_dim=32,
                                tanh_encoder_output=kwargs['tanh_encoder_output'])

        # Asymmetric Value function with access to the priv states
        critic_layers = []
        critic_layers.append(nn.Linear(num_critic_obs, critic_hidden_dims[0]))
        critic_layers.append(activation)
        for l in range(len(critic_hidden_dims)):
            if l == len(critic_hidden_dims) - 1:
                critic_layers.append(nn.Linear(critic_hidden_dims[l], 1))
            else:
                critic_layers.append(nn.Linear(critic_hidden_dims[l], critic_hidden_dims[l + 1]))
                critic_layers.append(activation)
        self.critic = nn.Sequential(*critic_layers)
        
        # Action noise
        if self.fix_action_std:
            action_std = self.kwargs.get("action_std", None)
            assert action_std is not None, "action_std must be provided"
            action_std = torch.tensor(action_std)
            self.std = nn.Parameter(action_std, requires_grad=False)
            print("the action std is fixed !!!")
        else:
            self.std = nn.Parameter(init_noise_std * torch.ones(num_actions))

    @staticmethod
    # not used at the moment
    def init_weights(sequential, scales):
        [torch.nn.init.orthogonal_(module.weight, gain=scales[idx]) for idx, module in
         enumerate(mod for mod in sequential if isinstance(mod, nn.Linear))]

    def reset(self, dones=None):
        pass

    def forward(self):
        raise NotImplementedError
    
    @property
    def action_mean(self):
        return self.distribution.mean

    @property
    def action_std(self):
        return self.distribution.stddev
    
    @property
    def entropy(self):
        return self.distribution.entropy().sum(dim=-1)

    def update_distribution(self, observations):
        mean = self.actor(observations)
        # has_nan_mean = torch.isnan(mean).any().item()
        # if has_nan_mean:
        #     print("mean has nan")
        #     has_nan_obs = torch.isnan(observations).any().item()
        #     if has_nan_obs:
        #         print("has nan obs: ", has_nan_obs)
        #     obs_array = observations.cpu().detach().numpy()
        #     np.savetxt('nan_obs.txt', obs_array)
        #     exit()
        self.distribution = Normal(mean, mean*0. + torch.clamp(self.std, 1e-3 * torch.ones_like(self.std),
                                                                8 * torch.ones_like(self.std)))

    def act(self, observations, **kwargs):
        self.update_distribution(observations)
        return self.distribution.sample()
    
    def get_actions_log_prob(self, actions):
        return self.distribution.log_prob(actions).sum(dim=-1)

    def act_inference(self, observations, eval=False, **kwargs):
        if not eval:
            actions_mean = self.actor(observations, eval)
            return actions_mean
        else:
            actions_mean = self.actor(observations, eval=True)
            return actions_mean

    def evaluate(self, observations, **kwargs):
        critic_observations = observations[:, :self.num_critic_obs]  # discard the long history
        value = self.critic(critic_observations)
        return value
    
    def reset_std(self, std, num_actions, device):
        new_std = std * torch.ones(num_actions, device=device)
        self.std.data = new_std.data