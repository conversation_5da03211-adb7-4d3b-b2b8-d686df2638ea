# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2021 ETH Zurich, Nikita Rudin
from legged_gym import LEGGED_GYM_ROOT_DIR, envs
import time
import os
from collections import deque
import statistics
# from torch.utils.tensorboard import SummaryWriter
import torch
import torch.optim as optim
import wandb
# import ml_runlog
import datetime
from tqdm import tqdm
import numpy as np
from rsl_rl.algorithms import PPORMA, PPO
from rsl_rl.modules import *
from rsl_rl.storage.replay_buffer import ReplayBuffer
from rsl_rl.env import VecEnv
import joblib
from copy import copy, deepcopy
from ruamel.yaml import YAML
import warnings
from legged_gym.utils.torch_humanoid_batch import HumanoidBatch
from rsl_rl.utils.running_mean_std import RunningMeanStd
from legged_gym.utils.torch_utils import dof_to_obs
from scipy.ndimage import gaussian_filter1d
import numpy as np
from scipy.spatial.transform import Rotation as sRot


CondKeyBodyIdx = [0, 4, 6, 7, 11, 13, 14, 18, 22, 24, 28, 30]

class OnPolicyRunner:

    def __init__(self,
                 env: VecEnv,
                 train_cfg,
                 log_dir=None,
                 init_wandb=True,
                 device='cpu', **kwargs):

        self.cfg=train_cfg["runner"]
        self.alg_cfg = train_cfg["algorithm"]
        self.policy_cfg = train_cfg["policy"]
        self.device = device
        self.env = env
        self.env_cfg = env.cfg.env
        self.normalize_obs = env.cfg.env.normalize_obs
        self.dagger_update_freq = self.alg_cfg.get("dagger_update_freq", 20)

        policy_class = eval(self.cfg["policy_class_name"])
        num_critic_obs = (self.env_cfg.n_proprio + self.env_cfg.num_task_observations + self.env_cfg.n_priv_latent
                          + self.env_cfg.n_priv + self.env_cfg.history_len * self.env_cfg.n_proprio)
        if self.env.cfg.motion_lib.enable_sparse_keyframe:
            num_critics = self.env.cfg.rewards.num_reward_groups
            reward_group_weights = self.env.cfg.rewards.reward_group_weights
        else:
            num_critics = 1
            reward_group_weights = None
        actor_critic = policy_class(num_prop=self.env.cfg.env.n_proprio,
                                    num_critic_obs=num_critic_obs,  # cfg.num_observations
                                    num_priv_latent=self.env.cfg.env.n_priv_latent,
                                    num_hist=self.env.cfg.env.history_len,
                                    num_future_steps=self.env_cfg.num_future_steps,
                                    num_task_obs=self.env_cfg.num_task_observations,
                                    num_actions=self.env.num_actions,
                                    num_critics=num_critics,
                                    **self.policy_cfg).to(self.device)
            
        if self.normalize_obs:
            self.normalizer = RunningMeanStd(insize=self.env.num_obs).to(self.device)
        else:
            self.normalizer = None
        
        alg_class = eval(self.cfg["algorithm_class_name"]) # PPO
        self.alg = alg_class(self.env, 
                                  actor_critic,
                                  device=self.device, **self.alg_cfg)
        self.num_steps_per_env = self.cfg["num_steps_per_env"]
        self.save_interval = self.cfg["save_interval"]
        self.eval_interval = self.cfg["eval_interval"]

        self.alg.init_storage(
            self.env.num_envs, 
            self.num_steps_per_env, 
            [self.env.num_obs], 
            [self.env.num_privileged_obs], 
            [self.env.num_actions],
            num_critics = num_critics,
            reward_group_weights = reward_group_weights,
        )

        self.learn = self.learn_RL
            
        # Log
        self.log_dir = log_dir
        self.writer = None
        self.tot_timesteps = 0
        self.tot_time = 0
        self.current_learning_iteration = 0
        self.current_steps = 0
        

    def learn_RL(self, num_learning_iterations, init_at_random_ep_len=False):
        mean_value_loss = 0.
        mean_surrogate_loss = 0.
        mean_disc_loss = 0.
        mean_disc_acc = 0.
        mean_hist_latent_loss = 0.
        mean_priv_reg_loss = 0. 
        priv_reg_coef = 0.
        entropy_coef = 0.
        grad_penalty_coef = 0.

        if init_at_random_ep_len:
            self.env.episode_length_buf = torch.randint_like(self.env.episode_length_buf, high=int(self.env.max_episode_length))
        obs = self.env.get_observations()
        privileged_obs = self.env.get_privileged_observations()
        critic_obs = privileged_obs if privileged_obs is not None else obs
        obs, critic_obs = obs.to(self.device), critic_obs.to(self.device)
        if self.normalize_obs:
            obs = self.normalizer(obs)
            
            self.normalizer.eval()
            critic_obs = self.normalizer(critic_obs)
            self.normalizer.train()
        infos = {}
        self.alg.actor_critic.train() # switch to train mode (for dropout for example)

        ep_infos, tracking_infos, dynamic_sampling_infos = [], [], []
        rewbuffer = deque(maxlen=100)
        rew_explr_buffer = deque(maxlen=100)
        rew_entropy_buffer = deque(maxlen=100)
        lenbuffer = deque(maxlen=100)
        cur_reward_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        cur_reward_explr_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        cur_reward_entropy_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        cur_episode_length = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)

        task_rew_buf = deque(maxlen=100)
        cur_task_rew_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)

        tot_iter = self.current_learning_iteration + num_learning_iterations
        self.start_learning_iteration = copy(self.current_learning_iteration)

        for it in range(self.current_learning_iteration, tot_iter):
            # evaluate the policy every eval interval
            if it % self.eval_interval == 0 and self.eval_interval > 0:
                eval_info = self.evaluate_policy(it)
                # save the filtered motion names as a yaml file
                save_yaml(eval_info["passed_keys"], file_name=f"passed_{it}", log_path=self.log_dir)
                save_yaml(eval_info["failed_keys"], file_name=f"failed_{it}", log_path=self.log_dir)
                if self.eval_interval == 1:
                    print("The evaluation process is finished, return !!!")
                    return 
            
            if self.env.cfg.motion_lib.collect_rollouts:
                motion_to_dump = self.collect_rollouts()
                motion_base_dir = self.env.cfg.motion_lib.motion_base.format(LEGGED_GYM_ROOT_DIR=LEGGED_GYM_ROOT_DIR)
                joblib.dump(motion_to_dump, open(os.path.join(motion_base_dir, "cmu_walk400_cycle1_push.pkl"), "wb"))
                print(f"Collected motion clips are stored at {motion_base_dir}")
                return

            start = time.time()
            hist_encoding = it % self.dagger_update_freq == 0
            # Rollout
            with torch.inference_mode():
                for i in range(self.num_steps_per_env):
                    actions = self.alg.act(obs, critic_obs, infos, hist_encoding, rollout_step=i)
                    # actions = self.alg.act(obs, critic_obs, infos, hist_encoding)
                    obs, privileged_obs, rewards, dones, infos = self.env.step(actions)  # obs has changed to next_obs !! if done obs has been reset
                    critic_obs = privileged_obs if privileged_obs is not None else obs
                    obs, critic_obs, rewards, dones = obs.to(self.device), critic_obs.to(self.device), rewards.to(self.device), dones.to(self.device)
                    
                    if self.normalize_obs:
                        obs = self.normalizer(obs)
                        
                        self.normalizer.eval()
                        critic_obs = self.normalizer(critic_obs)
                        self.normalizer.train()
                    
                    total_rew = self.alg.process_env_step(rewards, dones, infos)
                    
                    if self.log_dir is not None:
                        # Book keeping
                        if 'episode' in infos:
                            ep_infos.append(infos['episode'])
                        if "tracking" in infos:
                            tracking_infos.append(infos["tracking"])
                        if "dynamic_sampling" in infos:
                            dynamic_sampling_infos.append(infos["dynamic_sampling"])
                        # import ipdb; ipdb.set_trace()
                        cur_reward_sum += total_rew.sum(-1) if len(total_rew.shape) > 1 else total_rew
                        cur_reward_explr_sum += 0
                        cur_reward_entropy_sum += 0
                        cur_episode_length += 1

                        new_ids = (dones > 0).nonzero(as_tuple=False)

                        rewbuffer.extend(cur_reward_sum[new_ids][:, 0].cpu().numpy().tolist())
                        rew_explr_buffer.extend(cur_reward_explr_sum[new_ids][:, 0].cpu().numpy().tolist())
                        rew_entropy_buffer.extend(cur_reward_entropy_sum[new_ids][:, 0].cpu().numpy().tolist())
                        lenbuffer.extend(cur_episode_length[new_ids][:, 0].cpu().numpy().tolist())
                        
                        cur_reward_sum[new_ids] = 0
                        cur_reward_explr_sum[new_ids] = 0
                        cur_reward_entropy_sum[new_ids] = 0
                        cur_episode_length[new_ids] = 0
                stop = time.time()
                collection_time = stop - start

                # Learning step
                start = stop
                self.alg.compute_returns(critic_obs)
            
            regularization_scale = self.env.cfg.rewards.regularization_scale if hasattr(self.env.cfg.rewards, "regularization_scale") else 1
            average_episode_length = torch.mean(self.env.episode_length.float()).item() if hasattr(self.env, "episode_length") else 0
            if self.policy_cfg.get("teacher_policy", False):
                mean_value_loss, mean_surrogate_loss, mean_priv_reg_loss, mean_grad_penalty_loss, grad_penalty_coef, _ = self.alg.update()
            else:
                mean_value_loss, mean_surrogate_loss, mean_priv_reg_loss, priv_reg_coef, mean_grad_penalty_loss, grad_penalty_coef = self.alg.update()
            if hist_encoding and not self.cfg["algorithm_class_name"] == "PPO":
                print("Updating dagger...")
                mean_hist_latent_loss = self.alg.update_dagger()
            
            stop = time.time()
            learn_time = stop - start

            # save the training log and model
            if self.log_dir is not None:
                self.log(locals())
            if it < 2500:
                if it % self.save_interval == 0:
                    self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(it)))
            elif it < 5000:
                if it % (2*self.save_interval) == 0:
                    self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(it)))
            else:
                if it % (5*self.save_interval) == 0:
                    self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(it)))
            ep_infos.clear()
            tracking_infos.clear()
            dynamic_sampling_infos.clear()
        
        # self.current_learning_iteration += num_learning_iterations
        self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(self.current_learning_iteration)))
    
    @torch.inference_mode()
    def collect_rollouts(self):
        print("######################### Data Collection Start ################################")
        robot_mjcf_path = "{LEGGED_GYM_ROOT_DIR}/resources/robots/phybot/phybot_mark2_custom_collision.xml"
        robot_fk = HumanoidBatch(robot_mjcf_path.format(LEGGED_GYM_ROOT_DIR=LEGGED_GYM_ROOT_DIR))
        # hard encode here, should be opptimized later.
        dof_obs_size = 14*6
        dof_group_offsets = [0, 3, 4, 6, 7, 10, 11, 13, 14, 16, 18, 21, 23, 26, 28]
        dof_to_group_reindex = [1, 0, 2, 3, 5, 4, 6, 8, 7, 9, 10, 12, 11, 13,
                                15, 14, 17, 16,
                                19, 18, 20, 21, 22, 25, 24, 26, 27, 28]  # reindex to rpy order
        dof_group_types = [3, 1, 0, 1, 3, 1, 0, 1,
                           2, 4,
                           3, 4, 3, 4]  # hard encode the dof group types
        robot_dof_offset = [0, 3, 4, 6, 7, 10, 11, 13, 14, 16, 18, 21, 23, 24, 27, 29, 30]
        # setup the eval modes
        self.alg.test_mode()
        self.normalizer.eval()
        self.env.disable_reset = True
        odometry_reset_time_train = copy(self.env.global_trans_reset_time)
        self.env.global_trans_reset_time = 2  # disable using a large value
        cur_termination_dist_train = self.env.cur_termination_dist.clone()
        cur_termination_dist = 1.0
        self.env.cur_termination_dist = cur_termination_dist * torch.ones_like(cur_termination_dist_train)
        
        # setup the rollout buffer for dumping
        num_motions = self.env.num_motions
        num_motions_to_dump = self.env.cfg.motion_lib.rollout_cycle_times * num_motions
        max_motion_time_span = 60
        rollout_list = {"motions": [], "names": []} # store the effective rollout
        
        dump_motion_ids = torch.arange(num_motions, device=self.device, 
                                       dtype=torch.long).repeat(self.env.cfg.motion_lib.rollout_cycle_times)
        motion_ids_chunck = torch.split(dump_motion_ids, self.env.num_envs)
        num_mini_batch = len(motion_ids_chunck)

        # allow some kind of faliure cases
        while len(rollout_list["motions"]) < 0.9*num_motions_to_dump:
            for i in tqdm(range(num_mini_batch), desc="Num mini-batch"):
                print(f"Have collected {len(rollout_list['motions'])} motions so far, need to collect {num_motions_to_dump} in total.")
                max_step_possiable = int(max_motion_time_span / self.env.dt)
                pbar = tqdm(total=max_step_possiable, desc="Rollout steps")
                pbar.update(self.current_steps)
                # reset env for this iteration
                num_env_batch = motion_ids_chunck[i].shape[0]
                batch_env_ids = torch.arange(num_env_batch, device=self.device, dtype=torch.long)
                self.env.motion_ids[batch_env_ids] = motion_ids_chunck[i]
                self.env.motion_times[batch_env_ids] = self.env.motion_lib.sample_time(motion_ids_chunck[i], truncate_phase=0.85)
                self.env.reset_idx(batch_env_ids, init=True)
                batch_motion_time_spans = self.env.motion_lib.get_motion_tick_spans(motion_ids_chunck[i])
                batch_termination_states = torch.zeros(num_env_batch, dtype=torch.bool, device=self.device)
                rollout_step = 0
                # allocate the storage for this iteration.
                global_root_pos_list, global_root_vel_list, local_body_rot_quat_list = [], [], []
                global_facing_vector_list, global_key_body_pos_list = [], []
                max_gmpjpe_list = []

                while True:
                    obs = self.env.get_observations()
                    if self.normalize_obs:
                        obs = self.normalizer(obs)
                    actions = self.alg.actor_critic.act_inference(obs)
                    obs, critic_obs, rewards, dones, extra_info = self.env.step(actions.detach())

                    # post eval step
                    termination_states = torch.logical_and(
                        (rollout_step+1) * self.env.dt <= batch_motion_time_spans,
                        self.env.reset_buf[batch_env_ids]
                    )
                    batch_termination_states = torch.logical_or(batch_termination_states, termination_states)

                    # if there are any env that has not terminated, continue the rollout
                    if (~batch_termination_states).any():
                        cur_max_motion_time_span = batch_motion_time_spans[~batch_termination_states].max()
                        cur_max_motion_time_span = torch.clamp(cur_max_motion_time_span, min=0, max=max_motion_time_span)
                        if rollout_step * self.env.dt >= cur_max_motion_time_span:
                            cur_max_motion_time_span = (rollout_step + 1) * self.env.dt
                    else:
                        # all motions are terminated/failed, set the max time span to the longest possiable motion time span
                        cur_max_motion_time_span = batch_motion_time_spans.max()
                        cur_max_motion_time_span = torch.clamp(cur_max_motion_time_span, min=0, max=max_motion_time_span)
                    # step the rollout step
                    rollout_step += 1
                    pbar.update(1)
                    # aggregate the desired states
                    cur_root_states = self.env.root_states[batch_env_ids].clone()
                    global_root_pos = cur_root_states[:, 0:3]
                    global_root_rot_quat = cur_root_states[:, 3:7]
                    global_root_vel = cur_root_states[:, 7:10]
                    # current efftive joint rot groups are 13
                    pseudo_dof_pos = torch.zeros((num_env_batch, robot_fk.num_dof), device=self.device, dtype=torch.float32)
                    pseudo_dof_pos[:, self.env.ref_dof_indices] = self.env.dof_pos[batch_env_ids].clone()
                    local_joints_rot_quat = dof_to_obs(pseudo_dof_pos, dof_obs_size, dof_group_offsets, 
                                                       dof_to_group_reindex, dof_group_types, rot_repr="quat").reshape(num_env_batch, 14, 4)
                    local_body_rot_quat = torch.cat([global_root_rot_quat[:, None], local_joints_rot_quat], dim=1)
                    local_body_rot_quat_extend = [local_body_rot_quat[:, i] for i in range(local_body_rot_quat.shape[1])]
                    ignored_body_idx = [13, 16]
                    for body_id in ignored_body_idx:
                        local_body_rot_quat_extend.insert(body_id, torch.tensor([0, 0, 0, 1.], device=self.device).expand(num_env_batch, 4))
                    local_body_rot_quat_extend = torch.stack(local_body_rot_quat_extend, dim=1).cpu().numpy()  # (n_envs, 17, 4)

                    pose_aa_sk = sRot.from_quat(local_body_rot_quat_extend.reshape(-1, 4)).as_rotvec().reshape(num_env_batch, 17, 3)
                    pose_aa_sk = torch.from_numpy(pose_aa_sk).to(self.device).float()
                    pose_aa_robot = torch.zeros((pose_aa_sk.shape[0], robot_fk.num_dof, 3), dtype=torch.float32, device=self.device)
                    for dof_id in range(len(robot_dof_offset) - 1):
                        sub_joints = robot_dof_offset[dof_id + 1] - robot_dof_offset[dof_id]
                        for jnt in range(sub_joints):
                            pose_aa_robot[:, robot_dof_offset[dof_id] + jnt] = pose_aa_sk[:, 1 + dof_id] * \
                                                                        robot_fk.dof_axis[robot_dof_offset[dof_id] + jnt][None,].to(self.device)
                    pose_aa_robot = torch.cat([pose_aa_sk[:, 0:1], pose_aa_robot], dim=1).reshape(num_env_batch, robot_fk.num_dof+1, 3)
                    motion_dict = robot_fk.fk_batch(global_root_pos.cpu()[None, ], 
                                                    pose_aa=pose_aa_robot.cpu()[None, ],
                                                    return_full=True, fps=int(1/self.env.dt))
                    global_rigid_body_pos = motion_dict.global_trans.squeeze(0)
                    global_key_body_pos = global_rigid_body_pos[:, CondKeyBodyIdx]

                    # extract the whole body facing vector
                    global_facing_vector = extract_facing_vector(global_rigid_body_pos)
                    
                    max_gmpjpe = self.env.tracking_errors["max_gmpjpe"][batch_env_ids]
                    # store the data
                    global_root_pos_list.append(global_root_pos)
                    global_root_vel_list.append(global_root_vel)
                    local_body_rot_quat_list.append(local_body_rot_quat)
                    global_facing_vector_list.append(global_facing_vector)
                    global_key_body_pos_list.append(global_key_body_pos)
                    max_gmpjpe_list.append(max_gmpjpe)

                    # rollout of this minni_batch of motions is finished, go for the next if exists
                    if (rollout_step * self.env.dt >= cur_max_motion_time_span or batch_termination_states.all()):
                        # aggregate the evaluation results for this iteration
                        global_root_pos_traj = torch.stack(global_root_pos_list, dim=1)  # (num_batch_motion, max_motion_len, )
                        global_root_vel_traj = torch.stack(global_root_vel_list, dim=1)
                        local_body_rot_quat_traj = torch.stack(local_body_rot_quat_list, dim=1)
                        global_facing_vector_traj = torch.stack(global_facing_vector_list, dim=1)
                        global_key_body_pos_traj = torch.stack(global_key_body_pos_list, dim=1)
                        max_gmpjpe_traj = torch.stack(max_gmpjpe_list, dim=1)

                        # store the effective traj into the traj_list
                        for j, max_gmpjpe in enumerate(max_gmpjpe_traj):
                            if batch_termination_states[j]:
                                try:
                                    effective_frames = (max_gmpjpe >= cur_termination_dist).nonzero(as_tuple=False).flatten().min()
                                except:
                                    effective_frames = max_gmpjpe.shape[0]
                            else:
                                effective_frames = max_gmpjpe.shape[0]
                            traj_time_span = effective_frames * self.env.dt
                            # discard the traj that is too short
                            if traj_time_span < 1:
                                continue
                            frame_skip = 3
                            # post-process to get the smoothed tarj trans and facing quat
                            traj_trans, traj_quats, _ = extract_traj_with_facing_vec(global_root_pos_traj[j, :effective_frames:frame_skip].cpu().numpy(), 
                                                                                    global_facing_vector_traj[j, :effective_frames:frame_skip].cpu().numpy())
                            # cutoff the effective motion frames
                            rollout_list["motions"].append({
                                "global_root_position": global_root_pos_traj[j, :effective_frames:frame_skip].cpu().numpy(),
                                "global_track_body_position": global_key_body_pos_traj[j, :effective_frames:frame_skip].cpu().numpy(),
                                "local_body_rot_quat": local_body_rot_quat_traj[j, :effective_frames:frame_skip].cpu().numpy(),
                                "global_root_vel": global_root_vel_traj[j, :effective_frames:frame_skip].cpu().numpy(),

                                "traj_trans": traj_trans,
                                "traj_facing_quat": traj_quats,
                            })
                            rollout_list["names"].append(self.env.motion_lib.motion_names[motion_ids_chunck[i][j]])
                        break
                pbar.clear()
        
        pbar.close()    
        print(f"A total num of {len(rollout_list['motions'])} motions are collected.")
        print("######################### Data Collection End ##################################")
        return rollout_list


    @torch.inference_mode()
    def evaluate_policy(self, num_itr):
        print("######################### Evaluation Start ###################################")
        # setup the eval modes
        self.alg.test_mode()
        self.normalizer.eval()
        self.env.disable_reset = True
        
        odometry_reset_time_train = copy(self.env.global_trans_reset_time)
        self.env.global_trans_reset_time = 2  # disable using a large value

        cur_termination_dist_train = self.env.cur_termination_dist.clone()
        self.env.cur_termination_dist = 0.5 * torch.ones_like(cur_termination_dist_train)
        
        # allocate metrics buffer
        self.termination_states = torch.zeros(self.env.num_envs, dtype=torch.bool, device=self.device)
        self.current_steps, self.success_rate = 0, 0
        self.mpjpe, self.gmpjpe, self.max_gmpjpe = [], [], []
        self.mpjpe_all, self.gmpjpe_all, self.max_gmpjpe_all = [], [], []
        self.motion_finish_rates_all, self.termination_states_all = [], []

        num_motions, num_envs = self.env.num_motions, self.env.num_envs
        max_eval_rollouts = 10
        max_eval_iters = 1
        max_motion_time_span = 60  #in seconds
        eval_motion_ids = torch.arange(num_motions, device=self.device, dtype=torch.long).repeat(max_eval_rollouts)
        eval_motion_times = 0.5 * torch.rand_like(eval_motion_ids.float())  # allow some init offset
        # eval_motion_times = self.env.motion_lib.sample_time(eval_motion_ids, truncate_phase=0.85)
        # just evaluate the first chunk of motions to save the training time
        eval_motion_ids_chunk = [torch.split(eval_motion_ids, num_envs)[i] for i in range(max_eval_iters) ]
        eval_motion_times_chunk = [torch.split(eval_motion_times, num_envs)[i] for i in range(max_eval_iters)]
        num_eval_iters = len(eval_motion_ids_chunk)
        assert num_eval_iters <= max_eval_iters

        eval_string = f""
        eval_metrics_dict = {}
        # conduct the evaluation rollout.
        for i in tqdm(range(num_eval_iters)):
            num_env_batch = eval_motion_ids_chunk[i].shape[0]
            batch_env_ids = torch.arange(num_env_batch, device=self.device, dtype=torch.long)
            batch_motion_ids = eval_motion_ids_chunk[i]
            batch_motion_times = eval_motion_times_chunk[i]
            self.env.motion_ids[batch_env_ids] = batch_motion_ids
            self.env.motion_times[batch_env_ids] = batch_motion_times
            self.env.reset_idx(batch_env_ids, init=True)
            batch_motion_time_spans = self.env.motion_lib.get_motion_tick_spans(eval_motion_ids_chunk[i])

            ep_reward = torch.zeros(num_env_batch, dtype=torch.float32, device=self.device)
            ep_len = torch.zeros(num_env_batch, dtype=torch.float32, device=self.device)

            max_step_possiable = int(max_motion_time_span / self.env.dt)
            pbar = tqdm(total=max_step_possiable, desc="Rollout steps")
            pbar.update(self.current_steps)
            while True:
                obs = self.env.get_observations()
                if self.normalize_obs:
                    obs = self.normalizer(obs)
                actions = self.alg.actor_critic.act_inference(obs)
                obs, critic_obs, rewards, dones, extra_info = self.env.step(actions.detach())

                # post eval step
                termination_states = torch.logical_and(
                    (self.current_steps+1) * self.env.dt <= batch_motion_time_spans,
                    self.env.reset_buf[batch_env_ids]
                )
                self.termination_states[batch_env_ids] = torch.logical_or(self.termination_states[batch_env_ids],
                                                                        termination_states)
                # if there are any env that has not terminated, continue the evaluation
                if (~self.termination_states[batch_env_ids]).any():
                    cur_max_motion_time_span = batch_motion_time_spans[~self.termination_states[batch_env_ids]].max()
                    cur_max_motion_time_span = torch.clamp(cur_max_motion_time_span, min=0, max=max_motion_time_span)
                    if self.current_steps * self.env.dt >= cur_max_motion_time_span:
                        cur_max_motion_time_span = (self.current_steps + 1) * self.env.dt
                else:
                    # all motions are terminated/failed, set the max time span to the longest possiable motion time span
                    cur_max_motion_time_span = batch_motion_time_spans.max()
                    cur_max_motion_time_span = torch.clamp(cur_max_motion_time_span, min=0, max=max_motion_time_span)
                # step the evalaution step and get the per step tarcking error
                self.mpjpe.append(self.env.tracking_errors["mpjpe"][batch_env_ids])  # keep the per-motion info
                self.gmpjpe.append(self.env.tracking_errors["gmpjpe"][batch_env_ids])
                self.max_gmpjpe.append(self.env.tracking_errors["max_gmpjpe"][batch_env_ids])
                self.current_steps += 1
                # update the pbar
                pbar.update(1)

                # evaluation of this minn_batch of motions is finished, go for the next if exists
                if (self.current_steps*self.env.dt >= cur_max_motion_time_span or 
                    self.termination_states[batch_env_ids].all()):
                    self.current_steps = 0
                    # aggregate the evaluation results for this iteration
                    batch_mpjpe = torch.stack(self.mpjpe, dim=1)  # (num_batch_motion, max_motion_len, )
                    batch_gmjpe = torch.stack(self.gmpjpe, dim=1)
                    batch_max_gmjpe = torch.stack(self.max_gmpjpe, dim=1)
                    # calculated the mean metrics over the valid motion time span.
                    batch_motion_finish_rates = [(batch_max_gmjpe[motion_id, :int(motion_time_span/self.env.dt)] < 0.5).sum()
                                                 / int(motion_time_span/self.env.dt) for motion_id, motion_time_span in enumerate(batch_motion_time_spans)]
                    batch_mpjpe = [batch_mpjpe[motion_id, :int(motion_time_span/self.env.dt)].mean() for
                                    motion_id, motion_time_span in enumerate(batch_motion_time_spans)]
                    batch_gmjpe = [batch_gmjpe[motion_id, :int(motion_time_span/self.env.dt)].mean() for
                                    motion_id, motion_time_span in enumerate(batch_motion_time_spans)]
                    batch_max_gmjpe = [batch_max_gmjpe[motion_id, :int(motion_time_span/self.env.dt)].max() for
                                    motion_id, motion_time_span in enumerate(batch_motion_time_spans)]
                    
                    self.mpjpe_all.extend(batch_mpjpe)
                    self.gmpjpe_all.extend(batch_gmjpe)
                    self.max_gmpjpe_all.extend(batch_max_gmjpe)
                    self.motion_finish_rates_all.extend(batch_motion_finish_rates)
                    self.termination_states_all.append(self.termination_states[batch_env_ids].clone())

                    # empety the buffer
                    self.termination_states[batch_env_ids] = False
                    self.mpjpe, self.gmpjpe, self.max_gmpjpe = [], [], []
                    pbar.close()
                    break
        
        # aggregate the intermediate results after all evaluation iterations.
        # calculate the eval metrics of all motions
        mpjpe_all = torch.stack(self.mpjpe_all, dim=0)
        gmpjpe_all = torch.stack(self.gmpjpe_all, dim=0)
        max_gmpjpe_all = torch.stack(self.max_gmpjpe_all, dim=0)
        motion_finish_rates_all = torch.stack(self.motion_finish_rates_all, dim=0)
        motion_ids_all = torch.cat(eval_motion_ids_chunk, dim=0)

        motion_indices = [(index, torch.where(motion_ids_all == index, True, False)) for index in range(num_motions)]
        motion_mpjpe_all = torch.zeros(num_motions, dtype=torch.float32, device=self.device)
        motion_gmpjpe_all = torch.zeros(num_motions, dtype=torch.float32, device=self.device)
        motion_max_gmpjpe_all = torch.zeros(num_motions, dtype=torch.float32, device=self.device)
        unique_motion_finish_rates = torch.zeros(num_motions, dtype=torch.float32, device=self.device)
        # calculate per-unique motion metrics
        for index, motion_id in motion_indices:
            if motion_id.any():
                motion_mpjpe_all[index] = mpjpe_all[motion_id].mean()
                motion_gmpjpe_all[index] = gmpjpe_all[motion_id].mean()
                motion_max_gmpjpe_all[index] = max_gmpjpe_all[motion_id].mean()
                unique_motion_finish_rates[index] = motion_finish_rates_all[motion_id].mean()
        # derive the tracking error metric for data-filtering (by exbody2).
        motion_tracking_errors = 0.9*motion_mpjpe_all + 0.1*motion_gmpjpe_all
        filtering_threshold = 0.9 * 0.065 + 0.1 * 0.12
        passed_idx = (motion_tracking_errors < filtering_threshold).nonzero(as_tuple=False).flatten()
        failed_idx = (motion_tracking_errors >= filtering_threshold).nonzero(as_tuple=False).flatten()

        # the reported evalation metrics
        termination_states_all = torch.cat(self.termination_states_all, dim=0)
        success_idx_all = (~termination_states_all).nonzero(as_tuple=False).flatten().cpu()
        success_rate = success_idx_all.shape[0] / termination_states_all.shape[0]
        passed_rate = passed_idx.shape[0] / num_motions
        # calculate the eval metrics of the successful motions
        mpjpe_success = mpjpe_all[success_idx_all].mean().nan_to_num().item()
        gmpjpe_success = gmpjpe_all[success_idx_all].mean().nan_to_num().item()
        max_gmpjpe_success = max_gmpjpe_all[success_idx_all].mean().nan_to_num().item()
        mean_completion_rate = motion_finish_rates_all.mean().item()
        termination_dist = cur_termination_dist_train.clone().mean().item()

        # log the eval metrics to wandb and the terminal
        eval_metrics_dict.update({
            "Eval/mpjpe_success": mpjpe_success,
            "Eval/gmpjpe_success": gmpjpe_success,
            "Eval/max_gmpjpe_success": max_gmpjpe_success,
            "Eval/mpjpe_all": mpjpe_all.mean().item(),
            "Eval/gmpjpe_all": gmpjpe_all.mean().item(),
            "Eval/max_gmpjpe_all": max_gmpjpe_all.mean().item(),
            "Eval/success_rate": success_rate,
            "Eval/passed_rate": passed_rate,
            "Eval/completion_rate": mean_completion_rate,
            "Eval/termination_dist": termination_dist,
        })
        wandb.log(eval_metrics_dict, step=num_itr)
        for key, value in eval_metrics_dict.items():
            eval_string += f"""{f"{key}: ":>{35}} {value:.4f}\n"""
        print(eval_string)

        passed_keys = [self.env.motion_lib.motion_names[i] for i in passed_idx]
        failed_keys = [self.env.motion_lib.motion_names[i] for i in failed_idx]
        motion_keys = [self.env.motion_lib.motion_names[i] for i in range(num_motions)]
        if success_rate == 0:
            print("======================= No Success !!! =======================")

        # adjusted the motion sampling prob according to the success rate and reset the env motion_ids and motion times
        # add a func to the motionlib class for adjusting the motion weight
        # change the motion sample regime
        # prioritized_ids = self.env.motion_lib.update_sampling_probs(failed_idx)
        # prioritized_keys = self.env.motion_lib.motion_names[prioritized_ids.flatten().cpu()].tolist()
        self.env.motion_ids = torch.fmod(torch.arange(self.env.num_envs, dtype=torch.long, device=self.device), num_motions)
        # self.env.motion_ids = torch.multinomial(self.env.motion_lib.sampling_probs, self.env.num_envs, replacement=True).to(self.device)
        self.env.motion_times = self.env.motion_lib.sample_time(self.env.motion_ids, truncate_phase=0.85)
        self.reset_motion_times = self.env.motion_times.clone()
        # reset for upcoming training iteration.
        self.env.reset_idx(torch.arange(self.env.num_envs, device=self.device), init=True)
        # clear the eval_log 
        self.env.extras["episode"] = {}
        self.env.extras["tracking"] = {}
        
        # restore to training mode
        self.alg.train_mode()
        self.normalizer.train()
        self.env.disable_reset = False
        self.env.global_trans_reset_time = odometry_reset_time_train
        self.env.cur_termination_dist = cur_termination_dist_train

        print("######################### Evaluation End #####################################")
        # torch.cuda.empty_cache()
        del self.termination_states_all, self.motion_finish_rates_all, self.mpjpe_all, self.gmpjpe_all, self.max_gmpjpe_all
        return {"passed_keys": passed_keys, "failed_keys": failed_keys}

    def log(self, locs, width=80, pad=35):
        self.tot_timesteps += self.num_steps_per_env * self.env.num_envs
        self.tot_time += locs['collection_time'] + locs['learn_time']
        iteration_time = locs['collection_time'] + locs['learn_time']

        ep_string = f''
        wandb_dict = {}
        if locs['ep_infos']:
            for key in locs['ep_infos'][0]:
                infotensor = torch.tensor([], device=self.device)
                for ep_info in locs['ep_infos']:
                    # handle scalar and zero dimensional tensor infos
                    if not isinstance(ep_info[key], torch.Tensor):
                        ep_info[key] = torch.Tensor([ep_info[key]])
                    if len(ep_info[key].shape) == 0:
                        ep_info[key] = ep_info[key].unsqueeze(0)
                    infotensor = torch.cat((infotensor, ep_info[key].to(self.device)))
                value = torch.mean(infotensor).nan_to_num()
                # wandb_dict['Episode_rew/' + key] = value
                if "metric" in key:
                    wandb_dict['Episode_rew_metrics/' + key] = value
                else:
                    if "tracking" in key:
                        wandb_dict['Episode_rew_tracking/' + key] = value
                    elif "curriculum" in key:
                        wandb_dict['Episode_curriculum/' + key] = value
                    else:
                        wandb_dict['Episode_rew_regularization/' + key] = value
                    ep_string += f"""{f'Mean episode {key}:':>{pad}} {value:.4f}\n""" # dont print metrics
        if locs["tracking_infos"]:
            for key in locs["tracking_infos"][0]:
                infotensor = torch.tensor([], device=self.device)
                for tracking_info in locs["tracking_infos"]:
                    # handle scalar and zero dimensional tensor infos
                    if not isinstance(tracking_info[key], torch.Tensor):
                        tracking_info[key] = torch.Tensor([tracking_info[key]])
                    if len(tracking_info[key].shape) == 0:
                        tracking_info[key] = tracking_info[key].unsqueeze(0)
                    infotensor = torch.cat((infotensor, tracking_info[key].to(self.device)))
                value = torch.mean(infotensor).nan_to_num()
                wandb_dict['Episode_tracking_metrics/' + key] = value
                ep_string += f"""{f'Mean episode {key}:':>{pad}} {value:.4f}\n"""
        
        if locs["dynamic_sampling_infos"]:
            for key in locs["dynamic_sampling_infos"][0]:
                infotensor = torch.tensor([], device=self.device)
                for dynamic_sampling_info in locs["dynamic_sampling_infos"]:
                    if key not in dynamic_sampling_info:
                        continue
                    # handle scalar and zero dimensional tensor infos
                    if not isinstance(dynamic_sampling_info[key], torch.Tensor):
                        dynamic_sampling_info[key] = torch.Tensor([dynamic_sampling_info[key]])
                    if len(dynamic_sampling_info[key].shape) == 0:
                        dynamic_sampling_info[key] = dynamic_sampling_info[key].unsqueeze(0)
                    infotensor = torch.cat((infotensor, dynamic_sampling_info[key].to(self.device)))
                if len(infotensor) > 0:
                    value = torch.mean(infotensor).nan_to_num()
                    wandb_dict['Episode_dynamic_sampling/' + key] = value

        mean_std = self.alg.actor_critic.std.mean()
        fps = int(self.num_steps_per_env * self.env.num_envs / (locs['collection_time'] + locs['learn_time']))

        wandb_dict['Loss/value_func'] = locs['mean_value_loss']
        wandb_dict['Loss/surrogate'] = locs['mean_surrogate_loss']
        wandb_dict['Loss/entropy_coef'] = locs['entropy_coef']
        wandb_dict['Loss/grad_penalty_loss'] = locs.get('mean_grad_penalty_loss', 0)
        wandb_dict['Loss/learning_rate'] = self.alg.learning_rate

        wandb_dict['Adaptation/hist_latent_loss'] = locs['mean_hist_latent_loss']
        wandb_dict['Adaptation/priv_reg_loss'] = locs.get('mean_priv_reg_loss', 0)
        wandb_dict['Adaptation/priv_ref_lambda'] = locs['priv_reg_coef']

        wandb_dict['Scale/regularization_scale'] = locs["regularization_scale"]
        wandb_dict['Scale/grad_penalty_coef'] = locs["grad_penalty_coef"]
        wandb_dict["Scale/behavior_cloning_coef"] = locs.get("behavior_cloning_coef", 0)

        wandb_dict['Policy/mean_noise_std'] = mean_std.item()
        wandb_dict['Perf/total_fps'] = fps
        wandb_dict['Perf/collection time'] = locs['collection_time']
        wandb_dict['Perf/learning_time'] = locs['learn_time']
        if len(locs['rewbuffer']) > 0:
            wandb_dict['Train/mean_reward'] = statistics.mean(locs['rewbuffer'])
            wandb_dict['Train/mean_episode_length'] = statistics.mean(locs['lenbuffer'])

        wandb.log(wandb_dict, step=locs['it'])

        str = f" \033[1m Learning iteration {locs['it']}/{self.current_learning_iteration + locs['num_learning_iterations']} \033[0m "

        scale_str = f"""{'Regularization_scale:':>{pad}} {locs['regularization_scale']:.4f}\n"""
        average_episode_length = f"""{'Average_episode_length:':>{pad}} {locs['average_episode_length']:.4f}\n"""
        gp_scale_str = f"""{'Grad_penalty_coef:':>{pad}} {locs['grad_penalty_coef']:.4f}\n"""
        if len(locs['rewbuffer']) > 0:
            log_string = (f"""{'#' * width}\n"""
                          f"""{str.center(width, ' ')}\n\n"""
                          f"""{'Experiment Name:':>{pad}} {os.path.basename(self.log_dir)}\n\n"""
                          f"""{'Computation:':>{pad}} {fps:.0f} steps/s (collection: {locs[
                            'collection_time']:.3f}s, learning {locs['learn_time']:.3f}s)\n"""
                          f"""{'Value function loss:':>{pad}} {locs['mean_value_loss']:.4f}\n"""
                          f"""{'Surrogate loss:':>{pad}} {locs['mean_surrogate_loss']:.4f}\n"""
                          f"""{'Grad penalty loss:':>{pad}} {locs.get("mean_grad_penalty_loss", 0):.4f}\n"""
                          f"""{'Mean action noise std:':>{pad}} {mean_std.item():.2f}\n"""
                          f"""{'Mean reward (total):':>{pad}} {statistics.mean(locs['rewbuffer']):.2f}\n"""
                          f"""{'Mean episode length:':>{pad}} {statistics.mean(locs['lenbuffer']):.2f}\n""")
        else:
            log_string = (f"""{'#' * width}\n"""
                          f"""{str.center(width, ' ')}\n\n"""
                          f"""{'Computation:':>{pad}} {fps:.0f} steps/s (collection: {locs[
                            'collection_time']:.3f}s, learning {locs['learn_time']:.3f}s)\n"""
                          f"""{'Value function loss:':>{pad}} {locs['mean_value_loss']:.4f}\n"""
                          f"""{'Surrogate loss:':>{pad}} {locs['mean_surrogate_loss']:.4f}\n"""
                          f"""{'Mean action noise std:':>{pad}} {mean_std.item():.2f}\n""")

        log_string += f"""{'-' * width}\n"""
        log_string += ep_string
        log_string += f"""{'-' * width}\n"""
        log_string += scale_str
        log_string += average_episode_length
        log_string += gp_scale_str
        curr_it = locs['it'] - self.start_learning_iteration
        eta = self.tot_time / (curr_it + 1) * (locs['num_learning_iterations'] - curr_it)
        mins = eta // 60
        secs = eta % 60
        log_string += (f"""{'-' * width}\n"""
                       f"""{'Total timesteps:':>{pad}} {self.tot_timesteps}\n"""
                       f"""{'Iteration time:':>{pad}} {iteration_time:.2f}s\n"""
                       f"""{'Total time:':>{pad}} {self.tot_time:.2f}s\n"""
                       f"""{'ETA:':>{pad}} {mins:.0f} mins {secs:.1f} s\n""")
        print(log_string)

    def save(self, path, infos=None):
        if self.normalize_obs:
            state_dict = {
            'model_state_dict': self.alg.actor_critic.state_dict(),
            'optimizer_state_dict': self.alg.optimizer.state_dict(),
            'iter': self.current_learning_iteration,
            'normalizer': self.normalizer,
            'infos': infos,
            }
        else:
            state_dict = {
            'model_state_dict': self.alg.actor_critic.state_dict(),
            'optimizer_state_dict': self.alg.optimizer.state_dict(),
            'iter': self.current_learning_iteration,
            'infos': infos,
            }
        torch.save(state_dict, path)

    def load(self, path, load_optimizer=True):
        print("*" * 80)
        print("Loading model from {}...".format(path))
        loaded_dict = torch.load(path, map_location=self.device)
        self.alg.actor_critic.load_state_dict(loaded_dict['model_state_dict'])
        if self.normalize_obs:
            self.normalizer = loaded_dict['normalizer']
        if load_optimizer:
            self.alg.optimizer.load_state_dict(loaded_dict['optimizer_state_dict'])
        # self.current_learning_iteration = loaded_dict['iter']
        self.current_learning_iteration = int(os.path.basename(path).split("_")[1].split(".")[0])
        self.env.global_counter = self.current_learning_iteration * 24
        self.env.total_env_steps_counter = self.current_learning_iteration * 24
        print("*" * 80)
        return loaded_dict['infos']

    def get_inference_policy(self, device=None):
        self.alg.actor_critic.eval() # switch to evaluation mode (dropout for example)
        if device is not None:
            self.alg.actor_critic.to(device)
        return self.alg.actor_critic.act_inference
    
    def get_actor_critic(self, device=None):
        self.alg.actor_critic.eval() # switch to evaluation mode (dropout for example)
        if device is not None:
            self.alg.actor_critic.to(device)
        return self.alg.actor_critic
    
    def get_normalizer(self, device=None):
        self.normalizer.eval()
        if device is not None:
            self.normalizer.to(device)
        return self.normalizer


LeftHipIdx, RightHipIdx, LeftShoulderIdx, RightShoulderIdx = 1, 8, 17, 22
FacingVector = [1, 0, 0]

def extract_facing_vector(global_position):
    upper_across = global_position[:, LeftShoulderIdx, :] - global_position[:, RightShoulderIdx, :]
    lower_across = global_position[:, LeftHipIdx, :] - global_position[:, RightHipIdx, :]
    across = (upper_across / torch.sqrt((upper_across ** 2).sum(dim=-1))[..., None] +
              lower_across / torch.sqrt((lower_across ** 2).sum(dim=-1))[..., None])
    across = across / torch.sqrt((across ** 2).sum(dim=-1))[..., None]
    forward_vec = torch.linalg.cross(across, torch.tensor([[0, 0, 1.]], device=across.device))  # noqa
    return forward_vec


def extract_traj_with_facing_vec(root_positions, forwards, smooth_kernel=(5, 10)):
    traj_trans, traj_quats, filtered_forwards = [], [], []
    facing_vec0 = np.array([FacingVector])

    for kernel_size in smooth_kernel:
        smooth_traj = gaussian_filter1d(root_positions[:, [0, 1]], kernel_size, axis=0, mode='nearest')
        traj_trans.append(smooth_traj)

        forward = gaussian_filter1d(forwards, kernel_size, axis=0, mode='nearest')
        forward = forward / np.linalg.norm(forward, axis=-1, keepdims=True)
        filtered_forwards.append(forward)

        v0s = facing_vec0.repeat(len(forward), axis=0)
        xyz = np.cross(v0s, forward)  # noqa
        w = np.sqrt((v0s ** 2).sum(axis=-1) * (forward ** 2).sum(axis=-1)) + (v0s * forward).sum(axis=-1)
        between_xyzw = np.concatenate([xyz, w[..., np.newaxis]], axis=-1)
        # normalize the wxyz quat using sRot.
        between = sRot.from_quat(between_xyzw).as_quat()
        traj_quats.append(between)

    return traj_trans, traj_quats, filtered_forwards


def save_yaml(motion_keys, file_name, log_path):
    yaml = YAML()
    yaml.preserve_quotes = True  # Preserve quotes if necessary
    motion_data = {}
    for i, motion_name in enumerate(motion_keys):
        motion_data[str(motion_name)] = {
            "trim_beg": -1,  # Assuming these values are constant, modify as needed
            "trim_end": -1,
            "weight": 1.0,
            "difficulty": 4  # Modify as needed
        }
    # Constructing the final dictionary structure
    final_structure = {
        "motions": motion_data
    }
    # Writing to a YAML file
    with open(os.path.join(log_path, f'motions_{file_name}.yaml'), 'w') as file:
        yaml.dump(final_structure, file)
    print('YAML file created with the specified structure.')
