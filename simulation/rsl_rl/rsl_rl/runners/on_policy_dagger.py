import os
import torch
import time
from collections import deque
from copy import copy, deepcopy
from rsl_rl.modules import *
from rsl_rl.runners.on_policy_runner import OnPolicyRunner
from rsl_rl.env import VecEnv
from rsl_rl.utils.running_mean_std import RunningMeanStd
from rsl_rl.algorithms import PPO


class OnPolicyDagger(OnPolicyRunner):
    def __init__(self, 
                 env: VecEnv,
                 train_cfg,
                 log_dir=None,
                 init_wandb=True,
                 device='cpu', **kwargs):
        self.cfg=train_cfg["runner"]
        self.alg_cfg = train_cfg["algorithm"]
        self.policy_cfg = train_cfg["policy"]
        self.device = device
        self.env = env
        self.env_cfg = env.cfg.env
        self.normalize_obs = env.cfg.env.normalize_obs
        # init the expert policy class
        expert_policy_class = eval(self.cfg["expert_class_name"])
        self.expert_policy = expert_policy_class(num_prop=self.env.cfg.env.n_proprio,
                                                  num_critic_obs=self.env.num_privileged_obs,  # cfg.num_observations
                                                  num_priv_latent=self.env.cfg.env.n_priv_latent,
                                                  num_hist=self.env.cfg.env.expert_history_len,
                                                  num_future_steps=self.env_cfg.num_future_steps,
                                                  num_task_obs=self.env_cfg.num_task_observations,
                                                  num_actions=self.env.num_actions,
                                                  **train_cfg["expert_policy"]).to(self.device)
        # load the pre-trained expert policy
        assert self.cfg["resume_expert"], "Must load a pre-trained expert policy for Dagger!"
        proj_path = os.path.dirname(log_dir)
        expert_path = os.path.join(proj_path, self.cfg["load_expert_id"])
        if self.cfg["checkpoint_expert"] == -1:
            models = [file for file in os.listdir(expert_path) if "model" in file]
            models.sort(key = lambda m: "{0:0>15}".format(m))
            model = models[-1]
        else:
            model = "model_{}.pt".format(self.cfg["checkpoint_expert"])
        expert_path = os.path.join(expert_path, model)
        loaded_dict = torch.load(expert_path, map_location=self.device)
        self.expert_policy.load_state_dict(loaded_dict['model_state_dict'])
        if self.normalize_obs:
            self.expert_normalizer = loaded_dict["normalizer"]
            self.expert_normalizer.eval()
        print("Expert policy {} loaded !!!".format(self.cfg["checkpoint_expert"]))
        # set the expert policy to be eval mode
        for param in self.expert_policy.parameters():
                param.requires_grad = False
        self.expert_policy.eval()  # Just incase
        # expose the act head
        self.expert_policy = self.expert_policy.act_inference

        ## construct a student learner using the dual hist actor critic
        policy_class = eval(self.cfg["policy_class_name"])
        num_critic_obs = self.env.num_privileged_obs
        actor_critic = policy_class(
            num_prop=self.env.cfg.env.n_proprio,
            num_actions=self.env.num_actions,
            num_critic_obs=num_critic_obs,
            num_priv_explicit=self.env.cfg.env.n_priv,
            num_priv_latent=self.env.cfg.env.n_priv_latent,
            short_hist_len=self.env.cfg.env.short_history_len,
            long_hist_len=self.env.cfg.env.long_history_len,
            num_future_steps=self.env_cfg.num_future_steps,
            num_task_obs=self.env_cfg.num_task_observations,
            **self.policy_cfg
        )
        if self.normalize_obs:
            self.normalizer = RunningMeanStd(insize=self.env.num_obs).to(self.device)
            self.critic_obs_normalizer = RunningMeanStd(insize=self.env.num_privileged_obs).to(self.device)
        else:
            self.normalizer = None
            self.critic_obs_normalizer = None
        
        alg_class = eval(self.cfg["algorithm_class_name"]) # PPO
        self.alg = alg_class(self.env, 
                                  actor_critic,
                                  device=self.device, **self.alg_cfg)
        self.num_steps_per_env = self.cfg["num_steps_per_env"]
        self.save_interval = self.cfg["save_interval"]
        self.eval_interval = self.cfg["eval_interval"]
        
        self.alg.init_storage(
            self.env.num_envs,
            self.num_steps_per_env,
            [self.env.num_obs],
            [self.env.num_privileged_obs],
            [self.env.num_actions],
            expert_actions_shape=[self.env.num_actions, ] # for expert actions storage
        )

        # override using the dagger specific methods and configs
        self.learn = self.learn_Dagger

        # Log
        self.log_dir = log_dir
        self.writer = None
        self.tot_timesteps = 0
        self.tot_time = 0
        self.current_learning_iteration = 0
        self.current_steps = 0

    def learn_Dagger(self, num_learning_iterations, init_at_random_ep_len=False):
        mean_value_loss = 0.
        mean_surrogate_loss = 0.
        mean_disc_loss = 0.
        mean_disc_acc = 0.
        mean_hist_latent_loss = 0.
        mean_priv_reg_loss = 0. 
        priv_reg_coef = 0.
        entropy_coef = 0.
        grad_penalty_coef = 0.
        mean_behavior_cloning_loss = 0.
        if init_at_random_ep_len:
            self.env.episode_length_buf = torch.randint_like(self.env.episode_length_buf, high=int(self.env.max_episode_length))

        obs = self.env.get_observations()
        privileged_obs = self.env.get_privileged_observations()
        critic_obs = privileged_obs if privileged_obs is not None else obs
        obs, critic_obs = obs.to(self.device), critic_obs.to(self.device)
        if self.normalize_obs:
            obs = self.normalizer(obs)
            critic_obs = self.critic_obs_normalizer(critic_obs)
            expert_obs = self.expert_normalizer(privileged_obs)

        self.alg.actor_critic.train() # switch to train mode
        
        infos = {}
        ep_infos, tracking_infos, dynamic_sampling_infos = [], [], []
        rewbuffer = deque(maxlen=100)
        rew_explr_buffer = deque(maxlen=100)
        rew_entropy_buffer = deque(maxlen=100)
        lenbuffer = deque(maxlen=100)
        cur_reward_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        cur_reward_explr_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        cur_reward_entropy_sum = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)
        cur_episode_length = torch.zeros(self.env.num_envs, dtype=torch.float, device=self.device)

        tot_itr = num_learning_iterations
        self.start_learning_iteration = copy(self.current_learning_iteration)

        for it in range(self.current_learning_iteration, tot_itr):
            start = time.time()
            hist_encoding = True  # long-short hist encoding for implicit state adaption
            with torch.inference_mode():
                for i in range(self.num_steps_per_env):
                    actions = self.alg.act(obs, critic_obs, infos, hist_encoding)
                    expert_actions = self.expert_policy(expert_obs)
                    # add the expert action into the rollout transition
                    self.alg.transition.expert_actions = expert_actions.detach()
                    obs, privileged_obs, rewards, dones, infos = self.env.step(actions)
                    critic_obs = privileged_obs if privileged_obs is not None else obs
                    obs, critic_obs, rewards, dones = obs.to(self.device), critic_obs.to(self.device), rewards.to(self.device), dones.to(self.device)

                    if self.normalize_obs:
                        obs = self.normalizer(obs)
                        critic_obs = self.critic_obs_normalizer(critic_obs)
                        expert_obs = self.expert_normalizer(privileged_obs)
                    
                    total_rew = self.alg.process_env_step(rewards, dones, infos)
                    
                    if self.log_dir is not None:
                        # Book keeping
                        if 'episode' in infos:
                            ep_infos.append(infos['episode'])
                        if "tracking" in infos:
                            tracking_infos.append(infos["tracking"])
                        if "dynamic_sampling" in infos:
                            dynamic_sampling_infos.append(infos["dynamic_sampling"])
                        # import ipdb; ipdb.set_trace()
                        cur_reward_sum += total_rew.sum(-1) if len(total_rew.shape) > 1 else total_rew
                        cur_reward_explr_sum += 0
                        cur_reward_entropy_sum += 0
                        cur_episode_length += 1

                        new_ids = (dones > 0).nonzero(as_tuple=False)
                        
                        rewbuffer.extend(cur_reward_sum[new_ids][:, 0].cpu().numpy().tolist())
                        rew_explr_buffer.extend(cur_reward_explr_sum[new_ids][:, 0].cpu().numpy().tolist())
                        rew_entropy_buffer.extend(cur_reward_entropy_sum[new_ids][:, 0].cpu().numpy().tolist())
                        lenbuffer.extend(cur_episode_length[new_ids][:, 0].cpu().numpy().tolist())
                        
                        cur_reward_sum[new_ids] = 0
                        cur_reward_explr_sum[new_ids] = 0
                        cur_reward_entropy_sum[new_ids] = 0
                        cur_episode_length[new_ids] = 0
                stop = time.time()
                collection_time = stop - start

                # Learning step
                start = stop
                self.alg.compute_returns(critic_obs)

            regularization_scale = self.env.cfg.rewards.regularization_scale if hasattr(self.env.cfg.rewards, "regularization_scale") else 1
            average_episode_length = torch.mean(self.env.episode_length.float()).item() if hasattr(self.env, "episode_length") else 0
            mean_value_loss, mean_surrogate_loss, behavior_cloning_coef, mean_grad_penalty_loss, grad_penalty_coef, mean_behavior_cloning_loss = self.alg.update()

            stop = time.time()
            learn_time = stop - start
            # save the training log and model
            if self.log_dir is not None:
                self.log(locals())
            if it < 2500:
                if it % self.save_interval == 0:
                    self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(it)))
            elif it < 5000:
                if it % (2*self.save_interval) == 0:
                    self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(it)))
            else:
                if it % (5*self.save_interval) == 0:
                    self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(it)))
            ep_infos.clear()
            tracking_infos.clear()
            dynamic_sampling_infos.clear()
        
        # self.current_learning_iteration += num_learning_iterations
        self.save(os.path.join(self.log_dir, 'model_{}.pt'.format(self.current_learning_iteration)))
