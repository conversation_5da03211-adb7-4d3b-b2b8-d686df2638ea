<?xml version="1.0" encoding="utf-8"?>

<launch>

  <!-- Name of the robot description parameter -->
  <arg name="description_name"         default="legged_description"/>
  <!-- Set robot description path -->
  <arg name="description_file"         default="$(find berkeley_humanoid_description)/urdf/robot.urdf"/>
  <!-- Joint state topic name -->
  <arg name="joint_states_topic"       default="/joint_states"/>

  <!-- Load robot description -->
  <include file="$(find berkeley_humanoid_description)/launch/load.launch">
    <arg name="description_name" value="$(arg description_name)"/>
    <arg name="description_file" value="$(arg description_file)"/>
  </include>

  <!-- Joint state publisher -->
  <node name="joint_state_publisher" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui" output="screen">
    <param name="use_gui"           value="true"/>
    <param name="rate"              value="100"/>
    <remap from="robot_description" to="$(arg description_name)"/>
    <remap from="joint_states"      to="$(arg joint_states_topic)"/>
  </node>

  <!-- Robot state publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" output="screen">
    <param name="publish_frequency" value="100"/>
    <param name="use_tf_static"     value="true"/>
    <remap from="robot_description" to="$(arg description_name)"/>
    <remap from="joint_states"      to="$(arg joint_states_topic)"/>
  </node>

  <!-- RViz -->
  <node name="rviz" pkg="rviz" type="rviz"
        args="-d $(find berkeley_humanoid_description)/config/rviz/standalone.rviz"
        output="screen"/>

</launch>
