from legged_gym import LEGGED_GYM_ROOT_DIR, envs
import isaacgym
import argparse
from isaacgym.torch_utils import *
import os
import time
import numpy as np
import mujoco, mujoco_viewer
from tqdm import tqdm
from collections import deque
from scipy.spatial.transform import Rotation as R
import torch
from legged_gym.utils.motion_lib_base import MotionLib
from legged_gym.envs import LEGGED_GYM_ROOT_DIR
from legged_gym.envs.base.legged_robot import euler_from_quaternion
from legged_gym.envs.phybot.phybot_mark2_mimic import transform_global_to_local
from pynput import keyboard


class CMD:
    vx = 0.
    vy = 0.0
    dyaw = 0.0
    gait_type = 1


def on_press(key):
    speed_increment = 0.2
    try:
        if key.char == '8':  # 增加 vx
            CMD.vx += speed_increment
        elif key.char == '5':  # 减少 vx
            CMD.vx -= speed_increment
        elif key.char == '4':  # 减少 vx
            CMD.dyaw += speed_increment
            print("the key enabled")
        elif key.char == '6':  # 减少 vx
            CMD.dyaw -= speed_increment
        elif key == keyboard.Key.down:  # 减少 vx
            CMD.vx -= speed_increment
        elif key == "7":  # 增加 vy
            CMD.vy += speed_increment
        elif key == "9":  # 减少 vy
            CMD.vy -= speed_increment
        # if key.char == ',':  # 增加 wz (对应 < 键)
        #     CMD.dyaw += speed_increment
        # elif key.char == '.':  # 减少 wz (对应 > 键)
        #     CMD.dyaw -= speed_increment
    except AttributeError:
        pass


def on_release(key):
    if key == keyboard.Key.esc:
        return False


def get_load_path(root, load_run=-1, checkpoint=-1, model_name_include="jit"):
    if checkpoint==-1:
        models = [file for file in os.listdir(root) if model_name_include in file]
        models.sort(key=lambda m: '{0:0>15}'.format(m))
        model = models[-1]
        checkpoint = model.split("_")[-1].split(".")[0]
    return model, checkpoint


def quatToEuler(quat):
    eulerVec = np.zeros(3)
    qw = quat[0] 
    qx = quat[1] 
    qy = quat[2]
    qz = quat[3]
    # roll (x-axis rotation)
    sinr_cosp = 2 * (qw * qx + qy * qz)
    cosr_cosp = 1 - 2 * (qx * qx + qy * qy)
    eulerVec[0] = np.arctan2(sinr_cosp, cosr_cosp)

    # pitch (y-axis rotation)
    sinp = 2 * (qw * qy - qz * qx)
    if np.abs(sinp) >= 1:
        eulerVec[1] = np.copysign(np.pi / 2, sinp)  # use 90 degrees if out of range
    else:
        eulerVec[1] = np.arcsin(sinp)

    # yaw (z-axis rotation)
    siny_cosp = 2 * (qw * qz + qx * qy)
    cosy_cosp = 1 - 2 * (qy * qy + qz * qz)
    eulerVec[2] = np.arctan2(siny_cosp, cosy_cosp)
    
    return eulerVec


def quat_to_projected_gravity(global_root_rot):
    """Transform global root rotation quaternion to projected gravity.

    Args:
        global_root_rot: Quaternion array of shape (..., 4) in (x,y,z,w) format

    Returns:
        projected_gravity: Array of shape (..., 3) representing gravity vector in root frame
    """
    # Define world-space gravity vector pointing downward
    gravity_vec = np.array([0.0, 0.0, -1], dtype=np.float32)

    # If input has batch dimension, expand gravity vector
    if len(global_root_rot.shape) > 1:
        gravity_vec = np.tile(gravity_vec, (global_root_rot.shape[0], 1))

    # Quaternion conjugate (inverse for unit quaternions)
    quat_conj = global_root_rot.copy()
    quat_conj[..., :3] *= -1

    # Rotate gravity vector using quaternion rotation
    def quat_rotate(q, v):
        q_w = q[..., 3]
        q_xyz = q[..., :3]
        t = 2.0 * np.cross(q_xyz, v)
        return v + q_w[..., None] * t + np.cross(q_xyz, t)

    projected_gravity = quat_rotate(quat_conj, gravity_vec)
    return projected_gravity


# mujoco sim
class HumanoidEnv:
    def __init__(self, policy_path, robot_type="phybot_mini", device="cuda", record_video=False):
        self.robot_type = robot_type
        self.device = device
        self.record_video = record_video
        if robot_type == "phybot_mini":
            model_path = f"{LEGGED_GYM_ROOT_DIR}/resources/robots/phybot_mini/phybot_mini_mark1.xml"
            self.stiffness = np.array([
                100, 150, 150, 150, 150, 30,
                100, 150, 150, 150, 150, 30,
                150, 
                150, 150, 30, 150,
                150, 150, 30, 150,
            ])
            self.damping = np.array([
                20, 10, 10, 10, 10, 6,
                20, 10, 10, 10, 10, 6,
                10, 
                10, 10, 6, 10,
                10, 10, 6, 10,
            ])
            self.control_indices = np.array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20])
            self.num_actions = len(self.control_indices)
            self.num_dofs = 21
            self.default_dof_pos = np.array([
                -0.2, 0.0, 0.0, 0.4, -0.2, 0.0,  # left leg (6)
                -0.2, 0.0, 0.0, 0.4, -0.2, 0.0,  # right leg (6)
                0.0,
                0.0, 0.0, 0.0, 0.0,
                0.0, 0.0, 0.0, 0.0,
            ])
            self.default_dof_pos_torch = torch.from_numpy(self.default_dof_pos).float().to(self.device)
            self.torque_limits = np.array([
                200, 80, 58, 80, 58, 9,
                200, 80, 58, 80, 58, 9,
                58, 
                58, 58, 9, 58,
                58, 58, 9, 58,
            ])
            self.action_scale = 0.1
            self.n_key_bodies = 10
            self.feet_idx = [6, 12]
            self.ref_kb_indices = [0, 13, 4, 6, 10, 12, 17, 19, 21, 23]
            self.ref_dof_indices = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22]
        else:
            raise ValueError(f"Robot type {robot_type} not supported!")
        
        if self.record_video:
            self.sim_duration = 10.0
        else:
            self.sim_duration = 60.0
        self.sim_dt = 0.002
        self.sim_decimation = 10
        self.control_dt = self.sim_dt * self.sim_decimation
        
        self.model = mujoco.MjModel.from_xml_path(model_path)
        self.model.opt.timestep = self.sim_dt
        self.data = mujoco.MjData(self.model)
        mujoco.mj_resetDataKeyframe(self.model, self.data, 0)
        mujoco.mj_step(self.model, self.data)
        if self.record_video:
            self.viewer = mujoco_viewer.MujocoViewer(self.model, self.data, 'offscreen')
        else:
            self.viewer = mujoco_viewer.MujocoViewer(self.model, self.data)
        self.viewer.cam.distance = 5.0
        
        self.obs_indices = torch.arange(self.num_dofs, dtype=torch.long, device=self.device)
        self.last_action = torch.zeros(self.num_actions, dtype=torch.float32, device=self.device)
        self.n_priv = 0
        self.n_proprio = 3 + 3 + 3 + 3 * self.num_actions
        self.num_commands = 3
        self.long_history_len = 20
        self.short_history_len = 5

        self.dof_pos_scale = 1.0
        self.dof_vel_scale = 0.05
        self.ang_vel_scale = 0.25
        self.lin_vel_scale = 2.0
        self.ang_vel_clip = 0.1
        self.lin_vel_clip = 0.1
        
        self.proprio_history_buf = deque(maxlen=self.long_history_len)
        for _ in range(self.long_history_len):
            self.proprio_history_buf.append(torch.zeros(self.n_proprio, dtype=torch.float32, device=self.device))

        self.num_envs = 1
        self.up_axis_idx = 2
        self.root_commands = torch.zeros((self.num_envs, self.num_commands), dtype=torch.float32, device=self.device)
        self.gravity_vec = to_torch(get_axis_params(-1., self.up_axis_idx), device=self.device).repeat((self.num_envs, 1))
        self.forward_vec = to_torch([1., 0., 0.], device=self.device).repeat((self.num_envs, 1))
        self.dt = self.control_dt

        # load the pretrained policy
        print("Loading jit for policy: ", policy_path)
        self.policy_path = policy_path
        self.policy_jit = torch.jit.load(policy_path, map_location=self.device)

        self.last_time = time.time()
    
    def extract_proprio_obs(self):
        quat = self.data.sensor('orientation').data.astype(np.float32)
        proj_grav = quat_to_projected_gravity(quat[[1, 2, 3, 0]])
        ang_vel = self.data.sensor('angular-velocity').data.astype(np.float32)
        dof_pos = self.data.qpos.astype(np.float32)[-self.num_dofs:]
        dof_vel = self.data.qvel.astype(np.float32)[-self.num_dofs:]
        return (torch.from_numpy(ang_vel).float().to(self.device),
                torch.from_numpy(proj_grav).float().to(self.device),
                torch.from_numpy(dof_pos).float().to(self.device),
                torch.from_numpy(dof_vel).float().to(self.device))
    
    # get from the keyborad or joystick
    def set_root_commands(self):
        self.root_commands[:, 0] = torch.ones_like(self.root_commands[:, 0]) * CMD.vx
        self.root_commands[:, 1] = torch.ones_like(self.root_commands[:, 1]) * CMD.vy
        self.root_commands[:, 2] = torch.ones_like(self.root_commands[:, 2]) * CMD.dyaw

        self.root_commands[:, 0] *= torch.abs(self.root_commands[..., 0]) > self.lin_vel_clip
        self.root_commands[:, 1] *= torch.abs(self.root_commands[..., 1]) > self.lin_vel_clip
        self.root_commands[:, 2] *= torch.abs(self.root_commands[..., 2]) > self.ang_vel_clip
        
    def run(self):
        if self.record_video:
            import imageio
            video_name = f"{self.robot_type}_{''.join(os.path.basename(self.policy_path).split('.')[:-1])}.mp4"
            path = f"../../logs/mujoco_videos/"
            if not os.path.exists(path):
                os.makedirs(path)
            video_name = os.path.join(path, video_name)
            mp4_writer = imageio.get_writer(video_name, fps=50)
        
        # save the processed motion frames for deployment.
        leg_torques = []
        # enter the simulation loop
        listener = keyboard.Listener(on_press=on_press, on_release=on_release)
        listener.start()
        for i in tqdm(range(int(self.sim_duration / self.sim_dt)), desc="Running simulation..."):
            ang_vel, proj_grav, dof_pos, dof_vel = self.extract_proprio_obs()
            priv_latent = torch.zeros((1, 4 + 1 + 2 * self.num_actions + 3), dtype=torch.float32, device=self.device)
            self.dof_pos = dof_pos
            self.set_root_commands()
            # print("current dof pos are", i, self.dof_pos)
            if i % self.sim_decimation == 0:
                obs_prop = torch.cat([
                    self.root_commands.squeeze(0),
                    ang_vel * self.ang_vel_scale,
                    proj_grav,
                    (dof_pos - self.default_dof_pos_torch) * self.dof_pos_scale,
                    dof_vel * self.dof_vel_scale,
                    self.last_action,
                ])
                assert obs_prop.shape[0] == self.n_proprio, f"Expected {self.n_proprio} but got {obs_prop.shape[0]}"
                obs_hist = torch.cat(list(self.proprio_history_buf), dim=0).flatten()
                self.proprio_history_buf.append(obs_prop)
                obs_buf = torch.cat([obs_prop[None, ], priv_latent, obs_hist[None, ]], dim=-1)

                with torch.no_grad():
                    raw_action = self.policy_jit(obs_buf).cpu().numpy().squeeze()
                
                self.last_action = torch.from_numpy(raw_action).float().to(self.device)
                raw_action = np.clip(raw_action, -50., 50.)
                scaled_actions = raw_action * self.action_scale
                
                step_actions = np.zeros(self.num_dofs)
                step_actions[self.control_indices] = scaled_actions
                
                pd_target = step_actions + self.default_dof_pos

                self.viewer.cam.lookat = self.data.qpos.astype(np.float32)[:3]
                if self.record_video:
                    img = self.viewer.read_pixels()
                    mp4_writer.append_data(img)
                else:
                    self.viewer.render()
                
            torque = (pd_target - dof_pos.cpu().numpy()) * self.stiffness - dof_vel.cpu().numpy() * self.damping
            torque = np.clip(torque, -self.torque_limits, self.torque_limits)
            
            self.data.ctrl = torque
            leg_torques.append(torque[self.control_indices].copy())
            
            mujoco.mj_step(self.model, self.data)
        
        self.viewer.close()
        if self.record_video:
            mp4_writer.close()
                 

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    
    parser.add_argument('--robot', type=str, default="gr1") # options: gr1, h1, berkeley, g1
    parser.add_argument('--proj_name', type=str, default="phybot_mark2_mimic")
    parser.add_argument('--exptid', type=str, required=True)
    parser.add_argument('--checkpoint', type=int, default=-1)
    parser.add_argument('--record_video', action='store_true')
    args = parser.parse_args()
    
    policy_pth = "../../logs/{}/{}/traced/".format(args.proj_name, args.exptid)
    assert os.path.exists(policy_pth), f"Policy path {policy_pth} does not exist!"
    model_name = "model_{}-jit.pt".format(args.checkpoint)
    jit_policy_pth = os.path.join(policy_pth, model_name)
    print(f"Loading model from: {jit_policy_pth}")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    env = HumanoidEnv(policy_path=jit_policy_pth,
                      robot_type=args.robot, device=device, record_video=args.record_video)
    
    env.run()
