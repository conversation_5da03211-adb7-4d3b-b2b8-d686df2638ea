import os, sys
from statistics import mode
sys.path.append("../../../rsl_rl")
import torch
import torch.nn as nn
from rsl_rl.modules.actor_critic_rma import Actor, StateHistoryEncoder, get_activation
from rsl_rl.modules.dual_hist_actor_critic import DualHistActor
import argparse
import code
import shutil


def get_load_path(root, load_run=-1, checkpoint=-1, model_name_include="model"):
    if not os.path.isdir(root):  # use first 4 chars to mactch the run name
        model_name_cand = os.path.basename(root)
        model_parent = os.path.dirname(root)
        model_names = os.listdir(model_parent)
        model_names = [name for name in model_names if os.path.isdir(os.path.join(model_parent, name))]
        for name in model_names:
            if len(name) >= 6:
                if name[:6] == model_name_cand:
                    root = os.path.join(model_parent, name)
    if checkpoint==-1:
        models = [file for file in os.listdir(root) if model_name_include in file]
        models.sort(key=lambda m: '{0:0>15}'.format(m))
        model = models[-1]
        checkpoint = model.split("_")[-1].split(".")[0]
    else:
        model = "model_{}.pt".format(checkpoint) 

    load_path = os.path.join(root, model)
    return load_path, checkpoint


class HardwareRefNN(nn.Module):
    def __init__(self,  num_prop,
                        num_task_obs,
                        long_history_len,
                        short_history_len,
                        num_actions,
                        actor_hidden_dims=[1024, 512, 256, ],
                        activation='elu',
                        ):
        super().__init__()
        num_obs = num_prop + num_task_obs + long_history_len*num_prop
        self.num_obs = num_obs
        activation = get_activation(activation)
        self.normalizer = None
        
        self.actor = DualHistActor(
            num_prop=num_prop,
            num_actions=num_actions,
            actor_hidden_dims=actor_hidden_dims,
            activation=activation,
            short_hist_len=short_history_len,
            long_hist_len=long_history_len,
            num_task_obs=num_task_obs,
            num_priv_explicit=0,
            num_priv_latent=0,
            short_hist_embed_dim=32,
            tanh_encoder_output=False,
        )

    def load_normalizer(self, normalizer):
        self.normalizer = normalizer
        self.normalizer.eval()

    def forward(self, obs):
        assert obs.shape[1] == self.num_obs, f"Expected {self.num_obs} but got {obs.shape[1]}"
        obs = self.normalizer(obs)
        return self.actor(obs, hist_encoding=False, eval=False)


def play(args):
    load_run = "../../logs/{}/{}".format(args.proj_name, args.exptid)
    checkpoint = args.checkpoint
    
    if args.robot == "phybot_mini":
        num_actions = 21
        num_dofs = 21
        n_key_bodies = 10
        
    else:
        raise ValueError(f"Robot {args.robot} not supported!")

    n_proprio = 4 + 3 + 3 + 3 * num_actions
    long_history_len = 20
    short_history_len = 5
    num_task_obs_inst = (1 + 6 + 3 + 3 + num_dofs + 2 * n_key_bodies * 3)
    num_future_steps = 3
    num_task_observations = num_task_obs_inst * num_future_steps

    device = torch.device('cpu')
    policy = HardwareRefNN(n_proprio, num_task_observations,
                           long_history_len, short_history_len,
                           num_actions).to(device)
    load_path, checkpoint = get_load_path(root=load_run, checkpoint=checkpoint)
    load_run = os.path.dirname(load_path)
    print(f"Loading model from: {load_path}")
    ac_state_dict = torch.load(load_path, map_location=device)
    policy.load_state_dict(ac_state_dict['model_state_dict'], strict=False)
    policy.load_normalizer(ac_state_dict['normalizer'])
    
    policy = policy.to(device)#.cpu()
    if not os.path.exists(os.path.join(load_run, "traced")):
        os.mkdir(os.path.join(load_run, "traced"))

    # Save the traced actor
    policy.eval()
    with torch.no_grad(): 
        num_envs = 1
        
        obs_input = torch.ones(num_envs, n_proprio + num_task_observations + long_history_len*n_proprio, device=device)
        print("obs_input shape: ", obs_input.shape)
        
        traced_policy = torch.jit.trace(policy, obs_input)
        
        # traced_policy = torch.jit.script(policy)
        save_path = os.path.join(load_run, "traced", args.exptid + "-" + str(checkpoint) + "-jit.pt")
        traced_policy.save(save_path)
        print("Saved traced_actor at ", os.path.abspath(save_path))
        print("Robot: ", args.robot)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--proj_name', type=str)
    parser.add_argument('--exptid', type=str)
    parser.add_argument('--checkpoint', type=int, default=-1)
    parser.add_argument('--robot', type=str, default="phybot_mini") # options: phybot_mini, phybot_mark2

    args = parser.parse_args()
    play(args)
