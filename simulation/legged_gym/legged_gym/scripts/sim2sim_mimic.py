from legged_gym import LEGGED_GYM_ROOT_DIR, envs
import isaacgym
import argparse
from isaacgym.torch_utils import *
import os
import time
import numpy as np
import mujoco, mujoco_viewer
from tqdm import tqdm
from collections import deque
from scipy.spatial.transform import Rotation as R
import torch
from loop_rate_limiters import RateLimiter
from legged_gym.utils.motion_lib_base import MotionLib
from legged_gym.envs import LEGGED_GYM_ROOT_DIR
from legged_gym.envs.base.legged_robot import euler_from_quaternion
from legged_gym.envs.phybot.phybot_mark2_mimic import transform_global_to_local
from scipy.interpolate import interp1d


def interpolate_joint_positions(
    joint_positions: np.ndarray,
    original_hz: int,
    target_hz: int,
    kind: str = 'cubic'
) -> np.ndarray:
    """
    Interpolates a 2D numpy array of joint positions from an original to a target frequency.
    Args:
        joint_positions (np.ndarray): The input array of shape (array_length, num_dofs).
        original_hz (int): The original frequency of the data (e.g., 50).
        target_hz (int): The desired frequency for the output data (e.g., 120).
        kind (str, optional): The type of interpolation. Common options are 'linear',
                              'quadratic', 'cubic'. 'cubic' is recommended for smooth
                              joint motion. Defaults to 'cubic'.

    Returns:
        np.ndarray: The interpolated array with a new length corresponding to the target frequency.
    """
    if joint_positions.ndim != 2:
        raise ValueError("Input array must be 2-dimensional (array_length, num_dofs).")

    original_length, num_dofs = joint_positions.shape
    x_original = np.arange(original_length)
    new_length = int(round(original_length * (target_hz / original_hz)))
    x_new = np.linspace(0, original_length - 1, new_length)
    interpolator = interp1d(
        x_original,
        joint_positions,
        axis=0,
        kind=kind,
        bounds_error=False,
        fill_value="extrapolate"
    )
    interpolated_joint_positions = interpolator(x_new)

    return interpolated_joint_positions


def get_load_path(root, load_run=-1, checkpoint=-1, model_name_include="jit"):
    if checkpoint==-1:
        models = [file for file in os.listdir(root) if model_name_include in file]
        models.sort(key=lambda m: '{0:0>15}'.format(m))
        model = models[-1]
        checkpoint = model.split("_")[-1].split(".")[0]
    return model, checkpoint


def quatToEuler(quat):
    eulerVec = np.zeros(3)
    qw = quat[0] 
    qx = quat[1] 
    qy = quat[2]
    qz = quat[3]
    # roll (x-axis rotation)
    sinr_cosp = 2 * (qw * qx + qy * qz)
    cosr_cosp = 1 - 2 * (qx * qx + qy * qy)
    eulerVec[0] = np.arctan2(sinr_cosp, cosr_cosp)

    # pitch (y-axis rotation)
    sinp = 2 * (qw * qy - qz * qx)
    if np.abs(sinp) >= 1:
        eulerVec[1] = np.copysign(np.pi / 2, sinp)  # use 90 degrees if out of range
    else:
        eulerVec[1] = np.arcsin(sinp)

    # yaw (z-axis rotation)
    siny_cosp = 2 * (qw * qz + qx * qy)
    cosy_cosp = 1 - 2 * (qy * qy + qz * qz)
    eulerVec[2] = np.arctan2(siny_cosp, cosy_cosp)
    
    return eulerVec


def quat_to_projected_gravity(global_root_rot):
    """Transform global root rotation quaternion to projected gravity.

    Args:
        global_root_rot: Quaternion array of shape (..., 4) in (x,y,z,w) format

    Returns:
        projected_gravity: Array of shape (..., 3) representing gravity vector in root frame
    """
    # Define world-space gravity vector pointing downward
    gravity_vec = np.array([0.0, 0.0, -1], dtype=np.float32)

    # If input has batch dimension, expand gravity vector
    if len(global_root_rot.shape) > 1:
        gravity_vec = np.tile(gravity_vec, (global_root_rot.shape[0], 1))

    # Quaternion conjugate (inverse for unit quaternions)
    quat_conj = global_root_rot.copy()
    quat_conj[..., :3] *= -1

    # Rotate gravity vector using quaternion rotation
    def quat_rotate(q, v):
        q_w = q[..., 3]
        q_xyz = q[..., :3]
        t = 2.0 * np.cross(q_xyz, v)
        return v + q_w[..., None] * t + np.cross(q_xyz, t)

    projected_gravity = quat_rotate(quat_conj, gravity_vec)
    return projected_gravity


# mujoco sim
class HumanoidEnv:
    def __init__(self, policy_path, motion_id, play_motion_id,
                 robot_type="phybot_mini", device="cuda", record_video=False):
        self.robot_type = robot_type
        self.device = device
        self.record_video = record_video
        if robot_type == "phybot_mini":
            model_path = f"{LEGGED_GYM_ROOT_DIR}/resources/robots/phybot_mini/phybot_mini_mark1.xml"
            # self.stiffness = np.array([
            #     50, 50, 50, 50, 50, 15,
            #     50, 50, 50, 50, 50, 15,
            #     50, 
            #     50, 50, 15, 50,
            #     50, 50, 15, 50,
            # ])
            # self.damping = np.array([
            #     10, 10, 10, 10, 10, 5,
            #     10, 10, 10, 10, 10, 5,
            #     10, 
            #     10, 10, 5, 10,
            #     10, 10, 5, 10,
            # ])
            self.stiffness = np.array([
                100, 100, 100, 100, 100, 30,
                100, 100, 100, 100, 100, 30,
                100, 
                100, 100, 30, 100,
                100, 100, 30, 100,
            ])
            self.damping = np.array([
                20, 20, 20, 20, 20, 4,
                20, 20, 20, 20, 20, 4,
                20, 
                20, 20, 4, 20,
                20, 20, 4, 20,
            ])
            self.control_indices = np.array([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20])
            self.num_actions = len(self.control_indices)
            self.num_dofs = 21
            self.default_dof_pos = np.array([
                -0.15, 0.0, 0.0, 0.3, -0.15, 0.0,  # left leg (6)
                -0.15, 0.0, 0.0, 0.3, -0.15, 0.0,  # right leg (6)
                0.0,
                0.0, 0.0, 0.0, 0.0,
                0.0, 0.0, 0.0, 0.0,
            ])
            self.default_dof_pos_torch = torch.from_numpy(self.default_dof_pos).float().to(self.device)
            self.torque_limits = np.array([
                200, 80, 58, 80, 58, 9,
                200, 80, 58, 80, 58, 9,
                58, 
                58, 58, 9, 58,
                58, 58, 9, 58,
            ])
            self.action_scale = 0.3
            self.n_key_bodies = 10
            self.feet_idx = [6, 12]
            self.ref_kb_indices = [0, 13, 4, 6, 10, 12, 17, 19, 21, 23]
            self.ref_dof_indices = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22]
        else:
            raise ValueError(f"Robot type {robot_type} not supported!")
        
        if self.record_video:
            self.sim_duration = 10.0
        else:
            self.sim_duration = 60.0
        self.sim_dt = 0.002
        self.sim_decimation = 10
        self.control_dt = self.sim_dt * self.sim_decimation
        
        self.model = mujoco.MjModel.from_xml_path(model_path)
        self.model.opt.timestep = self.sim_dt
        self.data = mujoco.MjData(self.model)
        mujoco.mj_resetDataKeyframe(self.model, self.data, 0)
        mujoco.mj_step(self.model, self.data)
        if self.record_video:
            self.viewer = mujoco_viewer.MujocoViewer(self.model, self.data, 'offscreen')
        else:
            self.viewer = mujoco_viewer.MujocoViewer(self.model, self.data)
        self.viewer.cam.distance = 5.0
        
        self.obs_indices = torch.arange(self.num_dofs, dtype=torch.long, device=self.device)
        self.last_action = torch.zeros(self.num_actions, dtype=torch.float32, device=self.device)
        self.n_priv = 0
        self.n_proprio = 4 + 3 + 3 + 3 * self.num_actions  # 73
        self.num_future_steps = 3
        self.num_interval_steps = 1
        self.long_history_len = 20
        self.short_history_len = 5
        self.num_task_obs_inst = (1 + 6 + 3 + 3 + self.num_dofs + 2 * self.n_key_bodies * 3)
        self.num_task_observations = self.num_task_obs_inst * self.num_future_steps

        self.dof_pos_scale = 1.0
        self.dof_vel_scale = 0.05
        self.ang_vel_scale = 0.25
        self.lin_vel_scale = 2.0
        
        self.proprio_history_buf = deque(maxlen=self.long_history_len)
        for _ in range(self.long_history_len):
            self.proprio_history_buf.append(torch.zeros(self.n_proprio, dtype=torch.float32, device=self.device))

        # load the specified ref motion.
        self.motion_lib = MotionLib(
            motion_file=f"{LEGGED_GYM_ROOT_DIR}/motion_base",
            robot_name=self.robot_type,
            device=self.device,
            pre_processed=False,
            feet_idx=self.feet_idx,
            specified_ids=motion_id,
        )
        self.num_envs = 1
        self.up_axis_idx = 2
        self.gravity_vec = to_torch(get_axis_params(-1., self.up_axis_idx), device=self.device).repeat((self.num_envs, 1))
        self.forward_vec = to_torch([1., 0., 0.], device=self.device).repeat((self.num_envs, 1))
        self.dt = self.control_dt
        self.play_motion_name = play_motion_id[0]
        self.play_motion_idx = torch.tensor(self.motion_lib.motion_names.index(play_motion_id[0]), device=self.device)

        # load the pretrained policy
        print("Loading jit for policy: ", policy_path)
        self.policy_path = policy_path
        self.policy_jit = torch.jit.load(policy_path, map_location=self.device)

        self.root_commands = torch.zeros((self.num_envs, 4), dtype=torch.float32, device=self.device)
        self.target_yaw = torch.zeros((self.num_envs, 1), dtype=torch.float32, device=self.device)
        self.ang_vel_clip = 0.1
        self.lin_vel_clip = 0.1
        self.last_time = time.time()
    
    def extract_proprio_obs(self):
        quat = self.data.sensor('orientation').data.astype(np.float32)
        proj_grav = quat_to_projected_gravity(quat[[1, 2, 3, 0]])
        ang_vel = self.data.sensor('angular-velocity').data.astype(np.float32)
        dof_pos = self.data.qpos.astype(np.float32)[-self.num_dofs:]
        dof_vel = self.data.qvel.astype(np.float32)[-self.num_dofs:]
        return (torch.from_numpy(ang_vel).float().to(self.device),
                torch.from_numpy(proj_grav).float().to(self.device),
                torch.from_numpy(dof_pos).float().to(self.device),
                torch.from_numpy(dof_vel).float().to(self.device))
    
    def extract_student_task_obs(self, motion_id, motion_times):
        flat_ids, flat_times, end_mask = self.next_ref_motion_indices(motion_id, motion_times)
        next_ref_motion = self.motion_lib.get_motion_state(flat_ids, flat_times)
        # extract the cmd/task obs from the ref motion
        next_ref_root_pos = next_ref_motion.rb_pos[:, 0].clone()
        next_ref_root_rot = next_ref_motion.rb_rot[:, 0].clone()
        next_ref_root_vel = next_ref_motion.rb_vel[:, 0].clone()
        next_ref_root_ang_vel = next_ref_motion.rb_ang_vel[:, 0].clone()
        next_ref_kb_pos = next_ref_motion.rb_pos[:, self.ref_kb_indices].clone()
        next_ref_kb_vel = next_ref_motion.rb_vel[:, self.ref_kb_indices].clone()
        # extract dof command
        next_ref_dof_pos = next_ref_motion.dof_pos[:, self.ref_dof_indices].clone()
        # extracted root command
        next_ref_root_height = next_ref_motion.rb_pos[:, 0, 2:3].reshape(self.num_envs, self.num_future_steps, 1)
        next_root_proj_grav = (quat_rotate_inverse(next_ref_root_rot, self.gravity_vec.repeat(self.num_future_steps, 1))
                               .reshape(self.num_envs, self.num_future_steps, 3)) # (n_env, n_steps, 3)

        next_root_facing_vec = (quat_rotate(next_ref_root_rot, self.forward_vec.repeat(self.num_future_steps, 1))
                                .reshape(self.num_envs, self.num_future_steps, 3))
        # _, _, next_target_yaw = euler_from_quaternion(next_ref_root_rot.reshape(self.num_envs, self.num_future_steps, 4)[:, 0])
        
        next_local_root_vel = quat_rotate_inverse(next_ref_root_rot, next_ref_root_vel).reshape(self.num_envs, self.num_future_steps, 3) 
        next_local_root_ang_vel = quat_rotate_inverse(next_ref_root_rot, next_ref_root_ang_vel).reshape(self.num_envs, self.num_future_steps, 3)

        # extract dof command
        next_dof_pos_diff = ((next_ref_dof_pos - self.dof_pos.clone().repeat(self.num_future_steps, 1))
                             .reshape(self.num_envs, self.num_future_steps, self.num_dofs))
        
        # extract local keypoint pos command
        next_local_kb_pos = (transform_global_to_local(next_ref_kb_pos, next_ref_root_pos, next_ref_root_rot)
                             .reshape(self.num_envs, self.num_future_steps, -1))  # (n_env*n_steps, n_kb, 3)
        next_local_kb_vel = (transform_global_to_local(next_ref_kb_vel, next_ref_root_vel, next_ref_root_rot)
                             .reshape(self.num_envs, self.num_future_steps, -1))
        
        student_task_obs = torch.cat([
            next_ref_root_height,
            next_root_proj_grav,
            next_root_facing_vec,
            next_local_root_vel * self.lin_vel_scale,
            next_local_root_ang_vel * self.ang_vel_scale,
            next_dof_pos_diff * self.dof_pos_scale,
            next_local_kb_pos,
            next_local_kb_vel,
        ], dim=-1).view(self.num_envs, -1)

        self.root_commands[:, 0:2] = next_local_root_vel[:, 0, 0:2]
        self.root_commands[:, 0] *= torch.abs(self.root_commands[..., 0]) > self.lin_vel_clip
        self.root_commands[:, 1] *= torch.abs(self.root_commands[..., 1]) > self.lin_vel_clip
        self.root_commands[:, 2] = next_local_root_ang_vel[:, 0, 2] * self.ang_vel_scale
        self.root_commands[:, 2] *= torch.abs(self.root_commands[..., 2]) > self.ang_vel_clip
        self.root_commands[:, 3] = next_ref_root_height[:, 0, 0]

        # update the target yaw for next step.
        motion_time_spans = self.motion_lib.get_motion_tick_spans(motion_id)
        motion_times = torch.fmod(motion_times, motion_time_spans)
        # cur_ref_motion = self.motion_lib.get_motion_state(motion_id, motion_times)
        # cur_ref_root_rot = cur_ref_motion.rb_rot[:, 0].clone()
        # _, _, self.target_yaw = euler_from_quaternion(cur_ref_root_rot)

        return student_task_obs, end_mask

    def next_ref_motion_indices(self, motion_ids, motion_times):
        time_offsets = self.dt * (1 + torch.arange(self.num_future_steps, device=self.device)
                                   * self.num_interval_steps)
        future_times = time_offsets.unsqueeze(0) + motion_times.unsqueeze(-1)  # (envs, fut_motion_steps)
        flat_ids = motion_ids.unsqueeze(-1).tile((1, self.num_future_steps)).view(-1)

        motion_time_spans = self.motion_lib.get_motion_tick_spans(flat_ids)
        flat_times = torch.fmod(future_times.view(-1), motion_time_spans)
        
        end_mask = flat_times.view(-1, self.num_future_steps)[:, 0] <= motion_times
    
        return flat_ids, flat_times, end_mask
        
    def run(self):
        if self.record_video:
            import imageio
            video_name = f"{self.robot_type}_{''.join(os.path.basename(self.policy_path).split('.')[:-1])}.mp4"
            path = f"../../logs/mujoco_videos/"
            if not os.path.exists(path):
                os.makedirs(path)
            video_name = os.path.join(path, video_name)
            mp4_writer = imageio.get_writer(video_name, fps=50)
        

        # save the processed motion frames for deployment.
        motion_frames, whole_dof_pos, whole_dof_vel = [], [], []
        amp_motion = {
            "joints_list": ["left_hip_pitch", "left_hip_roll", "left_hip_yaw", "left_knee", "left_ankle_pitch", "left_ankle_roll",
                            "right_hip_pitch", "right_hip_roll", "right_hip_yaw", "right_knee", "right_ankle_pitch", "right_ankle_roll",
                            "waist_yaw", 
                            "left_shoulder_pitch", "left_shoulder_roll", "left_shoulder_yaw", "left_elbow_pitch",
                            "right_shoulder_pitch", "right_shoulder_roll", "right_shoulder_yaw", "right_elbow_pitch"],
            "joint_positions": [],  # a list of numpy array
            "root_position": [],
            "root_quaternion": [],
            "fps": 50.0,
        }
        self.motion_times = torch.zeros(1, dtype=torch.float32, device=self.device)
        # enter the simulation loop
        rate = RateLimiter(frequency=50, warn=False)  # the control freq
        for i in tqdm(range(int(self.sim_duration / self.sim_dt)), desc="Running simulation..."):
            ang_vel, proj_grav, dof_pos, dof_vel = self.extract_proprio_obs()
            
            self.dof_pos = dof_pos
            if i % self.sim_decimation == 0:
                student_task_obs, end_mask = self.extract_student_task_obs(self.play_motion_idx, self.motion_times)
                obs_prop = torch.cat([
                    self.root_commands.squeeze(0),
                    ang_vel * self.ang_vel_scale,
                    proj_grav,
                    (dof_pos - self.default_dof_pos_torch) * self.dof_pos_scale,
                    dof_vel * self.dof_vel_scale,
                    self.last_action,
                ])
                assert obs_prop.shape[0] == self.n_proprio, f"Expected {self.n_proprio} but got {obs_prop.shape[0]}"
                obs_hist = torch.cat(list(self.proprio_history_buf), dim=0).flatten()
                self.proprio_history_buf.append(obs_prop)

                motion_frames.append(torch.cat([self.root_commands, student_task_obs], dim=-1).squeeze().cpu().numpy())
                obs_buf = torch.cat([obs_prop[None, ], student_task_obs, obs_hist[None, ]], dim=-1)

                with torch.no_grad():
                    raw_action = self.policy_jit(obs_buf).cpu().numpy().squeeze()
                
                self.last_action = torch.from_numpy(raw_action).float().to(self.device)
                raw_action = np.clip(raw_action, -50., 50.)
                scaled_actions = raw_action * self.action_scale
                
                step_actions = np.zeros(self.num_dofs)
                step_actions[self.control_indices] = scaled_actions
                
                pd_target = step_actions + self.default_dof_pos
                
                # step the motion_lib after the policy step
                self.motion_times += self.control_dt

                self.viewer.cam.lookat = self.data.qpos.astype(np.float32)[:3]
                if self.record_video:
                    img = self.viewer.read_pixels()
                    mp4_writer.append_data(img)
                else:
                    rate.sleep()
                    self.viewer.render()

                # amp_motion["joint_positions"].append(dof_pos.cpu().numpy()[self.control_indices])
                # amp_motion["root_position"].append(self.data.qpos.astype(np.float32)[:3])
                # amp_motion["root_quaternion"].append(self.data.qpos.astype(np.float32)[3:7][[1, 2, 3, 0]])
                # default stance pose
                # default_dof_pos_amp = np.array([
                # -0.2, 0.0, 0.0, 0.4, -0.2, 0.0,  # left leg (6)
                # -0.2, 0.0, 0.0, 0.4, -0.2, 0.0,  # right leg (6)
                # 0.0,
                # 0.0, 0.1, 0.0, 0.0,
                # 0.0, -0.1, 0.0, 0.0,
                # ])
                # amp_motion["joint_positions"].append(default_dof_pos_amp)
                # amp_motion["root_position"].append(np.array([0.0, 0.0, 0.65]))
                # amp_motion["root_quaternion"].append(np.array([0.0, 0.0, 0.0, 1.0]))

                joint_dof_19 = np.array([0, 1, 2, 3, 4, 5,
                                        6, 7, 8, 9, 10, 11,
                                        12,
                                        13, 14, 16,
                                        17, 18, 20])
                whole_dof_pos.append(dof_pos.cpu().numpy()[joint_dof_19])
                whole_dof_vel.append(dof_vel.cpu().numpy()[joint_dof_19])
                
            torque = (pd_target - dof_pos.cpu().numpy()) * self.stiffness - dof_vel.cpu().numpy() * self.damping
            torque = np.clip(torque, -self.torque_limits, self.torque_limits)
            
            self.data.ctrl = torque
            mujoco.mj_step(self.model, self.data)

            if any(end_mask):
                dof_pos = np.stack(whole_dof_pos, axis=0)
                dof_vel = np.stack(whole_dof_vel, axis=0)
                control_freq = int(1/ self.sim_dt / self.sim_decimation)
                dof_pos_to_save = interpolate_joint_positions(dof_pos, original_hz=control_freq, target_hz=120)
                dof_vel_to_save = interpolate_joint_positions(dof_vel, original_hz=control_freq, target_hz=120)
                # np.savetxt("motion_save.txt", np.concatenate([dof_pos_to_save, dof_vel_to_save], axis=-1), delimiter=' ')

                # np.save("mini_walk{0}.npy".format(self.play_motion_name), amp_motion)
                # np.save("mini_stance.npy", amp_motion)
                
                # motion_to_save = np.stack(motion_frames, axis=0)
                # control_freq = int(1/ self.sim_dt / self.sim_decimation)
                # np.savetxt('motion_{0}_{1}hz_poses.csv'.format(self.play_motion_name, control_freq), motion_to_save, delimiter=',')
                # print("End of motion reached, save the motion frames with shape {}".format(motion_to_save.shape))
                return
        
        self.viewer.close()
        if self.record_video:
            mp4_writer.close()
                 

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--robot', type=str, default="gr1") # options: gr1, h1, berkeley, g1
    parser.add_argument('--proj_name', type=str, default="phybot_mark2_mimic")
    parser.add_argument('--exptid', type=str, required=True)
    parser.add_argument('--checkpoint', type=int, default=-1)
    parser.add_argument('--record_video', action='store_true')
    args = parser.parse_args()
    
    policy_pth = "../../logs/{}/{}/traced/".format(args.proj_name, args.exptid)
    assert os.path.exists(policy_pth), f"Policy path {policy_pth} does not exist!"
    model_name = "model_{}-jit.pt".format(args.checkpoint)
    jit_policy_pth = os.path.join(policy_pth, model_name)
    print(f"Loading model from: {jit_policy_pth}")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    motion_id = ["114_14_poses", "02_01_poses", "91_62_poses", "140_07_poses", "20_02_poses", "08_05_poses",
                 "82_13_poses", "132_45_poses", "141_16_poses", "77_03_poses", "91_27_poses",
                   "squat01_poses", "07_05_poses", "Salsa_Dancing", "stomp_left01_poses", "50002_chicken_wings_poses",
                   "Hip_Hop_Dancing", "Hip_Hop_Dancing_2", "wave_left01_poses", "50022_one_leg_loose_poses", "07_03_poses",
                   "83_39_poses", "83_37_poses", "111_01_poses", "113_01_poses", "36_03_poses", "37_01_poses"]
    
    motion_id = ["105_36_poses", "02_01_poses",
                "113_01_poses", "111_01_poses", 
                "132_45_poses",  "91_62_poses"]

    play_motion_id = ["91_62_poses", ]  # 140_07_poses, 132_45_poses
    
    env = HumanoidEnv(policy_path=jit_policy_pth, motion_id=motion_id, play_motion_id=play_motion_id,
                      robot_type=args.robot, device=device, record_video=args.record_video)
    
    env.run()
