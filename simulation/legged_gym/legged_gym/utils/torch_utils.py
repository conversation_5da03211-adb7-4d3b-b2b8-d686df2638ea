# Copyright (c) 2018-2023, NVIDIA Corporation
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
#    list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
#    this list of conditions and the following disclaimer in the documentation
#    and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
#    contributors may be used to endorse or promote products derived from
#    this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


from isaacgym.torch_utils import *
import torch
from torch import nn
import torch.nn.functional as F
from torch import Tensor
from typing import List
from legged_gym.utils.rotation_conversions import _axis_angle_rotation

def project_to_norm(x, norm=5, z_type = "sphere"):
    if z_type == "sphere":
        x = x / (torch.norm(x, dim=-1, keepdim=True) / norm + 1e-8)
    elif z_type == "uniform":
        x = torch.clamp(x, -norm, norm)
    return x


@torch.jit.script
def get_local_rot_matrix_from_joint_pos(dof_pos: Tensor, euler_orders: List[List[str]]):
    """
    dof_pos: The joint position of the robot.
    euler_orders: The euler order of each joint group.
    """
    num_joint_groups = len(euler_orders)
    local_whole_body_rot_matrix = torch.zeros((dof_pos.shape[0], num_joint_groups, 3, 3), device=dof_pos.device)
    start_idx = 0
    for i, euler_order in enumerate(euler_orders):
        dof_offset = len(euler_order)
        group_dof_pos = dof_pos[:, start_idx:start_idx+dof_offset]
        matrices = [_axis_angle_rotation(axis, angle) for axis, angle in zip(euler_order, group_dof_pos.unbind(dim=-1))]
        if len(matrices) < 3:
            for axis in ["X", "Y", "Z"]:
                if axis not in euler_order:
                    matrices.append(_axis_angle_rotation(axis, torch.zeros_like(group_dof_pos[:, 0])))
        local_whole_body_rot_matrix[:, i] = torch.matmul(torch.matmul(matrices[0], matrices[1]), matrices[2])     
        start_idx += dof_offset
    return local_whole_body_rot_matrix


@torch.jit.script
def quat_diff_norm(quat1, quat2):
    quat1inv = quat_conjugate(quat1)
    mul = quat_mul(quat1inv, quat2)
    norm = mul[..., 3].clip(-1, 1).arccos() * 2
    # Trying both rotation directions
    norm = torch.min(norm, torch.pi * 2 - norm)
    return norm


@torch.jit.script
def my_quat_rotate(q, v):
    shape = q.shape
    q_w = q[:, -1]
    q_vec = q[:, :3]
    a = v * (2.0 * q_w**2 - 1.0).unsqueeze(-1)
    b = torch.cross(q_vec, v, dim=-1) * q_w.unsqueeze(-1) * 2.0
    c = q_vec * \
        torch.bmm(q_vec.view(shape[0], 1, 3), v.view(
            shape[0], 3, 1)).squeeze(-1) * 2.0
    return a + b + c

@torch.jit.script
def quat_to_angle_axis(q):
    # type: (Tensor) -> Tuple[Tensor, Tensor]
    # computes axis-angle representation from quaternion q
    # q must be normalized
    min_theta = 1e-5
    qx, qy, qz, qw = 0, 1, 2, 3

    sin_theta = torch.sqrt(1 - q[..., qw] * q[..., qw])
    angle = 2 * torch.acos(q[..., qw])
    angle = normalize_angle(angle)
    sin_theta_expand = sin_theta.unsqueeze(-1)
    axis = q[..., qx:qw] / sin_theta_expand

    mask = torch.abs(sin_theta) > min_theta
    default_axis = torch.zeros_like(axis)
    default_axis[..., -1] = 1

    angle = torch.where(mask, angle, torch.zeros_like(angle))
    mask_expand = mask.unsqueeze(-1)
    axis = torch.where(mask_expand, axis, default_axis)
    return angle, axis


@torch.jit.script
def angle_axis_to_exp_map(angle, axis):
    # type: (Tensor, Tensor) -> Tensor
    # compute exponential map from axis-angle
    angle_expand = angle.unsqueeze(-1)
    exp_map = angle_expand * axis
    return exp_map


@torch.jit.script
def quat_to_exp_map(q):
    # type: (Tensor) -> Tensor
    # compute exponential map from quaternion
    # q must be normalized
    angle, axis = quat_to_angle_axis(q)
    exp_map = angle_axis_to_exp_map(angle, axis)
    return exp_map


@torch.jit.script
def quat_to_tan_norm(q):
    # type: (Tensor) -> Tensor
    # represents a rotation using the tangent and normal vectors
    ref_tan = torch.zeros_like(q[..., 0:3])
    ref_tan[..., 0] = 1
    tan = my_quat_rotate(q, ref_tan)

    ref_norm = torch.zeros_like(q[..., 0:3])
    ref_norm[..., -1] = 1
    norm = my_quat_rotate(q, ref_norm)

    norm_tan = torch.cat([tan, norm], dim=len(tan.shape) - 1)
    return norm_tan


@torch.jit.script
def tan_norm_to_mat(tan_norm):
    B = tan_norm.shape[0]
    tan = tan_norm.view(-1, 2, 3)[:, 0]
    norm = tan_norm.view(-1, 2, 3)[:, 1]
    tan_n = F.normalize(tan, dim=-1)

    norm_n = norm - (tan_n * norm).sum(-1, keepdim=True) * tan_n
    norm_n = F.normalize(norm_n, dim=-1)

    cross = torch.cross(norm_n, tan_n)

    rot_mat = torch.stack([tan_n, cross, norm_n], dim=-1).reshape(B, -1, 3, 3)
    return rot_mat


# @torch.jit.script
# def tan_norm_to_quat(tan_norm):
#     B = tan_norm.shape[0]
#     rot_mat = tan_norm_to_mat(tan_norm)
#     quat_new = ptr.matrix_to_quaternion_ijkr(rot_mat).view(B, -1, 4)
#     return quat_new


@torch.jit.script
def euler_xyz_to_exp_map(roll, pitch, yaw):
    # type: (Tensor, Tensor, Tensor) -> Tensor
    q = quat_from_euler_xyz(roll, pitch, yaw)
    exp_map = quat_to_exp_map(q)
    return exp_map


@torch.jit.script
def exp_map_to_angle_axis(exp_map):
    min_theta = 1e-5

    angle = torch.norm(exp_map, dim=-1)
    angle_exp = torch.unsqueeze(angle, dim=-1)
    axis = exp_map / angle_exp
    angle = normalize_angle(angle)

    default_axis = torch.zeros_like(exp_map)
    default_axis[..., -1] = 1

    mask = torch.abs(angle) > min_theta
    angle = torch.where(mask, angle, torch.zeros_like(angle))
    mask_expand = mask.unsqueeze(-1)
    axis = torch.where(mask_expand, axis, default_axis)

    return angle, axis


@torch.jit.script
def exp_map_to_quat(exp_map):
    angle, axis = exp_map_to_angle_axis(exp_map)
    q = quat_from_angle_axis(angle, axis)
    return q


@torch.jit.script
def slerp(q0, q1, t):
    # type: (Tensor, Tensor, Tensor) -> Tensor
    cos_half_theta = torch.sum(q0 * q1, dim=-1)

    neg_mask = cos_half_theta < 0
    q1 = q1.clone()
    q1[neg_mask] = -q1[neg_mask]
    cos_half_theta = torch.abs(cos_half_theta)
    cos_half_theta = torch.unsqueeze(cos_half_theta, dim=-1)

    half_theta = torch.acos(cos_half_theta)
    sin_half_theta = torch.sqrt(1.0 - cos_half_theta * cos_half_theta)

    ratioA = torch.sin((1 - t) * half_theta) / sin_half_theta
    ratioB = torch.sin(t * half_theta) / sin_half_theta

    new_q = ratioA * q0 + ratioB * q1

    new_q = torch.where(torch.abs(sin_half_theta) < 0.001, 0.5 * q0 + 0.5 * q1, new_q)
    new_q = torch.where(torch.abs(cos_half_theta) >= 1, q0, new_q)

    return new_q


@torch.jit.script
def calc_heading(q):
    # type: (Tensor) -> Tensor
    # calculate heading direction from quaternion
    # the heading is the direction on the xy plane
    # q must be normalized
    # this is the x axis heading
    ref_dir = torch.zeros_like(q[..., 0:3])
    ref_dir[..., 0] = 1
    rot_dir = my_quat_rotate(q, ref_dir)

    heading = torch.atan2(rot_dir[..., 1], rot_dir[..., 0])
    return heading


@torch.jit.script
def calc_heading_quat(q):
    # type: (Tensor) -> Tensor
    # calculate heading rotation from quaternion
    # the heading is the direction on the xy plane
    # q must be normalized
    heading = calc_heading(q)
    axis = torch.zeros_like(q[..., 0:3])
    axis[..., 2] = 1

    heading_q = quat_from_angle_axis(heading, axis)
    return heading_q


@torch.jit.script
def calc_heading_quat_inv(q):
    # type: (Tensor) -> Tensor
    # calculate heading rotation from quaternion
    # the heading is the direction on the xy plane
    # q must be normalized
    heading = calc_heading(q)
    axis = torch.zeros_like(q[..., 0:3])
    axis[..., 2] = 1

    heading_q = quat_from_angle_axis(-heading, axis)
    return heading_q


@torch.jit.script
def dof_to_obs(pose: Tensor, dof_obs_size: int, dof_group_offsets: List[int], 
               dof_to_group_reindex: List[int], dof_group_types: List[int], rot_repr: str ="6d"):
    joints_obs_size = 6
    num_groups = len(dof_group_offsets) - 1

    if dof_to_group_reindex is not None:
        pose = pose[:, dof_to_group_reindex]
    assert len(dof_group_types) == num_groups

    assert pose.shape[-1] == dof_group_offsets[-1]  # total number of dofs
    dof_obs_shape = pose.shape[:-1] + (dof_obs_size,)
    dof_pose_quat_shape = pose.shape[:-1] + (4*len(dof_group_types),)
    dof_obs = torch.zeros(dof_obs_shape, device=pose.device)
    dof_pose_quat = torch.zeros(dof_pose_quat_shape, device=pose.device)

    for j in range(num_groups):
        group_offset = dof_group_offsets[j]
        group_type = dof_group_types[j]
        dof_size = dof_group_offsets[j + 1] - dof_group_offsets[j]
        joints_pose = pose[:, group_offset : (group_offset + dof_size)]

        # assume this is a spherical joint
        if dof_size == 3:
            assert group_type == 3
            joint_pose_q = exp_map_to_quat(joints_pose)
        elif dof_size == 2:
            joints_pose_pseudo = torch.zeros((joints_pose.shape[:-1] + (3,)),
                                         dtype=joints_pose.dtype, device=joints_pose.device)
            # the rp_ joints group
            if group_type == 0:
                joints_pose_pseudo[:, 0:2] = joints_pose
            # the r_y joints group
            elif group_type == 2:
                joints_pose_pseudo[:, 0] = joints_pose[: ,0]
                joints_pose_pseudo[:, 2] = joints_pose[: ,1]
            # the _py joints group
            else:
                assert group_type == 4, "Get a mis_specified joints group type"
                joints_pose_pseudo[:, 1:3] = joints_pose
            joint_pose_q = exp_map_to_quat(joints_pose_pseudo)
        elif dof_size == 1:
            axis = torch.tensor(
                [0.0, 1.0, 0.0], dtype=joints_pose.dtype, device=joints_pose.device
            )
            joint_pose_q = quat_from_angle_axis(joints_pose[..., 0], axis)
        else:
            joint_pose_q = None
            assert False, "Unsupported joint type"

        dof_pose_quat[:, 4*j : 4*(j+1)] = joint_pose_q
        joint_dof_obs = quat_to_tan_norm(joint_pose_q)
        dof_obs[:, (j * joints_obs_size) : ((j + 1) * joints_obs_size)] = joint_dof_obs

    assert (num_groups * joints_obs_size) == dof_obs_size
    if rot_repr == "quat":
        return dof_pose_quat
    else:
        return dof_obs


def activation_facotry(act_name):
    if act_name == 'relu':
        return nn.ReLU
    elif act_name == 'tanh':
        return nn.Tanh
    elif act_name == 'sigmoid':
        return nn.Sigmoid
    elif act_name == "elu":
        return nn.ELU
    elif act_name == "selu":
        return nn.SELU
    elif act_name == "silu":
        return nn.SiLU
    elif act_name == "gelu":
        return nn.GELU
    elif act_name == "softplus":
        nn.Softplus
    elif act_name == "None":
        return nn.Identity