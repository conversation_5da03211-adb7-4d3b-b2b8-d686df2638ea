from tqdm import tqdm
from pathlib import Path
from legged_gym.utils import torch_utils
import numpy as np
from scipy.signal import butter, filtfilt, find_peaks
import torch
import yaml
from tensordict import TensorDict, tensorclass

scaler_first = lambda x: x[..., np.array([3, 0, 1, 2])]


def generate_contact_labels(foot_vel_data, feet_height_data, gait_velocity, freq_mul=1) -> np.ndarray:
    """
    Generates ground truth contact labels for legged robots based on foot height data.

    Implements the algorithm described in the pseudocode.

    Args:
        foot_vel_data: A 2D numpy array (num_data, num_legs) containing
                          the height of each foot over time.
        gait: A string indicating the robot's gait ('trot', 'pronking', 'gallop').
              This determines the low-pass filter cutoff.

    Returns:
        A 2D numpy boolean array of the same shape as foot_vel_data,
        where True indicates contact and False indicates no contact (swing).

    Raises:
        ValueError: If an unsupported gait string is provided.
    """
    if foot_vel_data.ndim != 2:
        raise ValueError("foot_vel_data must be a 2D array (num_data, num_legs)")

    num_data, num_legs = foot_vel_data.shape
    contacts = np.zeros((num_data, num_legs), dtype=bool) # Initialize contacts to False
    filtered_foot_vel_data = np.zeros_like(foot_vel_data)
    # --- Filter Design Parameters ---
    # Order of the Butterworth filter
    filter_order = 4
    # Note: Assumes half_power_freq is normalized frequency (Wn = fc / (fs/2))
    # If it's absolute frequency in Hz, you need fs (sampling frequency)
    # and Wn = half_power_freq / (fs / 2)

    for l in range(num_legs): # Iterate through each leg
        leg_vel = foot_vel_data[:, l]
        leg_height = feet_height_data[:, l]
        filtered_vel = leg_vel
        
        # --- Set filter frequency based on gait ---
        half_power_freq = 0.04 + 0.01*(gait_velocity - 1.0) # Normalized cutoff frequency Wn
        
        # --- Apply Low-Pass Filter ---
        # Design Butterworth filter
        # btype='low' is default
        b, a = butter(filter_order, half_power_freq*freq_mul, btype='low', analog=False)
        # Apply filter using filtfilt for zero phase delay
        try:
             # filtfilt requires len(x) > padlen, padlen = 3 * max(len(a), len(b))
             padlen = 3 * max(len(a)-1, len(b)-1) # Default padlen calculation from filtfilt docs
             if len(leg_vel) > padlen:
                 filtered_vel = filtfilt(b, a, leg_vel)
             else:
                 # Handle cases where data is too short for filtfilt's default padding
                 # You might want simple forward filtering (filter) or skip filtering
                 print(f"Warning: Leg {l} data length ({len(leg_vel)}) too short for filtfilt padding ({padlen}). Skipping filtering or consider alternative.")
                 # Option 1: Skip filtering (might affect results)
                 filtered_vel = leg_vel
                 # Option 2: Use filter instead (will introduce phase delay)
                 # from scipy.signal import lfilter
                 # filtered_vel = lfilter(b, a, leg_vel)
        except ValueError as e:
             print(f"Warning: Error filtering leg {l}: {e}. Skipping filtering.")
             filtered_vel = leg_vel
        filtered_foot_vel_data[:, l] = filtered_vel

        # --- Extract Local Maxima and Minima Indices ---
        # find_peaks returns indices and properties dictionary; we only need indices [0]
        # Increase prominence threshold slightly to avoid noise peaks if needed
        local_max_prominence = (np.max(filtered_vel) - np.min(filtered_vel)) * 0.12 # Example: 5% of range
        local_min_prominence = (np.max(filtered_vel) - np.min(filtered_vel)) * 0.005
        
        max_idx, _ = find_peaks(filtered_vel, prominence=local_max_prominence)
        # Find minima by finding peaks in the inverted signal
        min_idx, _ = find_peaks(-filtered_vel, prominence=local_min_prominence)

        num_min = len(min_idx)
        num_max = len(max_idx)

        if num_min == 0 or num_max == 0:
            # Not enough features found to determine contacts for this leg
            print(f"Warning: Not enough minima ({num_min}) or maxima ({num_max}) found for leg {l}. Skipping contact detection for this leg.")
            # assume the foot is always in contact at the stance phase.
            contacts[:, l] = True
            continue # Skip to the next leg

        # --- Process Minima and Maxima to Find Contact Phases ---
        i = 0 # Pointer for min_idx
        j = 0 # Pointer for max_idx

        # Ensure the first feature is a minimum if data starts near contact,
        # or discard initial maxima occurring before the first minimum.
        # Similarly, handle trailing minima after the last maximum if needed.
        # This basic loop assumes alternating min/max pattern holds reasonably well.
        while i < num_min and j < num_max:
            # Find the next peak *after* the current potential contact start minimum
            # Advance j until max_idx[j] is after min_idx[i]
            while j < num_max and max_idx[j] <= min_idx[i]:
                j += 1
            if j >= num_max: # No more maxima found after the current minimum
                break

            current_max_idx = max_idx[j] # This is the peak *after* the potential contact phase

            # Store the starting minimum index for this potential contact phase
            initial_min_idx_this_phase = min_idx[i]

            # Find all consecutive minima before the next peak
            contact_end = -1 # Initialize contact_end
            count = 0
            potential_contact_indices = []

            while i < num_min and min_idx[i] < current_max_idx:
                contact_end = min_idx[i] # Update contact_end to the latest minimum found
                potential_contact_indices.append(min_idx[i])
                i = i + 1
                count = count + 1
            if contact_end == -1: # Should not happen if logic above is correct, but safety check
                 j += 1 # Move to next peak if no minima found before it
                 continue

            # Determine the actual contact start index
            contact_start = initial_min_idx_this_phase # Default start

            # If only one local minimum is found between the start and the next peak,
            # apply the heuristic to extend the contact phase backwards.
            if count == 1:
                local_min_id = min_idx[i-1]
                half_interval_len = int(16 // freq_mul)
                potential_contact_interval = filtered_vel[np.clip(local_min_id-half_interval_len, 0, num_data)
                 : np.clip(local_min_id+half_interval_len, 0, num_data)]
                acc = (potential_contact_interval[1:] - potential_contact_interval[:-1]) * (120/freq_mul)
                contact_duration = np.sum(np.abs(acc) < 3*gait_velocity)
                contact_start = contact_end - contact_duration
                contact_end += contact_duration
                contact_start = max(0, contact_start) # Ensure index is not negative

            # Mark the calculated contact phase as True
            # Ensure start is not after end (can happen with the count==1 heuristic if min is early)
            if contact_start <= contact_end:
                #  print(f"Debug Leg {l}: Marking contact from {contact_start} to {contact_end}")
                 contacts[contact_start : contact_end + 1, l] = True # +1 to include end index
            else:
                 print(f"Debug Leg {l}: Skipped marking, count=1 heuristic caused start ({contact_start}) > end ({contact_end})")

            # This j increment was moved down from the original pseudocode (line 43)
            # It should logically occur *after* processing the segment before max_idx[j]
            # No, keep it here: we've processed up to max_idx[j], so next iteration needs max_idx[j+1]
            # j = j + 1 # Move to the next peak for the next iteration (already done inside the loop?)
            # Correction: The outer loop structure processes one peak (max_idx[j]) per iteration.
            # The inner loop advances `i`. `j` should be advanced only once per outer loop iteration.
            # The `j += 1` inside the inner while loop was to skip peaks *before* the first min.
            # It seems the pseudocode implicitly intended `j` to advance only here (line 43).
            # Let's stick to the original pseudocode structure for `j`.

            j = j + 1 # Corresponds to line 43 in pseudocode
            
            if j == num_max and i < num_min:
                interval_len = int(32 // freq_mul)
                if i <= num_min - 1: # If this is the last minimum
                    contact_start = min_idx[i]
                    contact_end = np.minimum(min_idx[-1] + interval_len, num_data)
                    potential_contact_interval = filtered_vel[contact_start:contact_end]
                    acc = (potential_contact_interval[1:] - potential_contact_interval[:-1]) * (120/freq_mul)
                    contact_duration = np.sum(np.abs(acc) < 3*gait_velocity)
                    contact_end = np.minimum(min_idx[-1] + contact_duration, num_data)
                else:
                    contact_start = min_idx[i] - interval_len // 2
                    contact_end = np.minimum(min_idx[-1] + interval_len // 2, num_data)
                contacts[contact_start:contact_end, l] = True # Mark the rest of the data as contact
        
        # finally, we need to detect the stance case ignored because of the smoothness at the begining and ending.
        # stance_idx = np.logical_and(filtered_vel < 0.12, leg_height < 0.10)
        stance_idx = filtered_vel < 0.12
        contacts[stance_idx, l] = True

    return contacts, filtered_foot_vel_data


@tensorclass
class MotionState:
    """The motion state tensor dict as a unified reference for
    task observation """
    dof_pos: torch.Tensor
    dof_vel: torch.Tensor
    rb_pos: torch.Tensor
    rb_rot: torch.Tensor
    rb_vel: torch.Tensor
    rb_ang_vel: torch.Tensor
    root_pos: torch.Tensor = None
    root_rot: torch.Tensor = None
    root_vel: torch.Tensor = None
    root_ang_vel: torch.Tensor = None
    feet_contact_profile: torch.Tensor = None


class MotionLib(TensorDict):
    global_trans: torch.Tensor
    global_rot_quat: torch.Tensor
    global_vel: torch.Tensor
    global_ang_vel: torch.Tensor
    global_root_vel: torch.Tensor
    global_root_ang_vel: torch.Tensor
    dof_pos: torch.Tensor
    dof_vel: torch.Tensor

    key_body_ids: torch.Tensor = None
    motion_ids: torch.Tensor = None

    m_lib_cfg = None

    def __init__(self,
                 motion_file,
                 device=None,
                 robot_name="phybot_mark2",
                 resolve_type="InverseKinematics",
                 motion_folder=None,
                 pre_processed=False,
                 num_to_load=-1,
                 feet_idx=None,
                 scaler_last=True,
                 specified_ids=None,
                 **kwargs):
        super().__init__()
        self.scaler_last = scaler_last
        self.motion_device = device
        motion_base_dir = Path(motion_file)
        robot_name = robot_name
        self.feet_idx = feet_idx

        self._load_motions(motion_base_dir, robot_name, resolve_type, motion_folder,
                           pre_processed=pre_processed, num_to_load=num_to_load,
                           specified_ids=specified_ids)

    def _load_motions(self,
                      motion_base_dir: Path,
                      robot_name: str,
                      resolve_type: str,
                      motion_folder: str,
                      motion_file_suffix: str = "memmap",
                      pre_processed: bool = False,
                      num_to_load: int = -1,
                      specified_ids=None,
                      ):
        if not pre_processed:
            # motion data and their file entries
            motion_clips, motion_paths = [], []
            # motion specs collection
            motion_dts, motion_tick_spans, motion_frames, motion_weights = [], [], [], []
            motion_dir = motion_base_dir / resolve_type / str(robot_name + "_" + motion_file_suffix)
            files_path = [f for f in Path(motion_dir).glob(f'*.{motion_file_suffix}')]
            files_path.sort()
            if specified_ids is not None:
                specified_motion_list = specified_ids
                if isinstance(specified_ids, str) and specified_ids.endswith(".yaml"):
                    with open(motion_base_dir / specified_ids) as file:
                        specified_motions = yaml.load(file, yaml.CLoader)["motions"]
                        specified_motion_list = [motion_id for motion_id in specified_motions.keys()]
                motion_paths.extend([path for path in files_path if path.name.split(".")[0] in specified_motion_list])
                if len(motion_paths) == len(specified_ids):
                    print(f"*** Loaded the specified motion clips {specified_ids} ***")
            elif num_to_load > 0:
                assert num_to_load <= len(files_path), "The number of motion clips to load is larger than the total number"
                motion_paths = files_path[:num_to_load]
            else:
                motion_paths = files_path

            ### start process each motion clip  ###
            for motion_path in tqdm(motion_paths):
                # add a truncation for the number of motion clips to load
                cur_motion = TensorDict.load_memmap(str(motion_path), device=self.motion_device)
                motion_clips.append(cur_motion)
                # read some motion specs from the dumped motion/tensordict
                cur_dt = 1.0 / cur_motion["fps"][0]
                n_frame = cur_motion["global_trans"].shape[0]
                cur_tick_len = cur_dt * (n_frame - 1)
                motion_dts.append(cur_dt)
                motion_tick_spans.append(cur_tick_len)
                motion_frames.append(n_frame)
                # add the contact profile if not exists
                if "feet_contact_profile" not in cur_motion.keys():
                    feet_gv = cur_motion["global_vel"][:, self.feet_idx].clone().cpu().numpy()  # (n_frames, n_feet, 3)
                    feet_lin_gv = np.linalg.norm(feet_gv, axis=-1)  # (n_frames, n_feet)
                    mean_feet_lin_gv = np.mean(feet_lin_gv)  # mean_vel
                    feet_height = cur_motion["global_trans"][:, self.feet_idx, 2].clone().cpu().numpy()  # (n_frames, n_feet)
                    freq_multiplier = 120 / cur_motion["fps"][0].cpu().numpy()
                    feet_contact_from_vel, filtered_feet_vel = generate_contact_labels(feet_lin_gv, feet_height, gait_velocity=mean_feet_lin_gv,
                                                                                       freq_mul=freq_multiplier)
                    cur_motion["feet_contact_profile"] = torch.tensor(feet_contact_from_vel, dtype=torch.bool)

                #TODO: add the motion evaluation and featuring encoding here
                motion_weights.append(1.0)  # naive weight for average samping
            ### end process each motion clip  ###

            # aggregate all motion clips into a single tensordict
            motion_chunks = self.cat(motion_clips, dim=0)  # the stacked tensordict motions
            # the motion specs
            motion_specs = TensorDict({
                "motion_dt": torch.tensor(motion_dts, dtype=torch.float32),
                "motion_tick_span": torch.tensor(motion_tick_spans, dtype=torch.float32),
                "motion_frame": torch.tensor(motion_frames, dtype=torch.long),
                "motion_weight": torch.tensor(motion_weights, dtype=torch.float32),
                "motion_id": torch.arange(len(motion_clips), dtype=torch.long),
            }, batch_size=[len(motion_clips)], device=self.motion_device)
            self.motion_names = [path.name.split(".")[0] for path in motion_paths]
            # nesting the motion chunks and specs
            self.state_dict = TensorDict({
                "motion_chunks": motion_chunks,
                "motion_specs": motion_specs,
                "motion_names": self.motion_names,
            })
        else:
            # accelerated loading by loading the pre-processed memmap files
            self.state_dict = self.load_memmap(motion_base_dir/ "motion_dump" / f"{motion_folder}.memmap")  # noqa
            self.state_dict.to(self.motion_device)
            self.motion_names = self.state_dict["motion_names"]
        
        print("###### Have successfully loaded the motion library containing {} motions !! ######".format(len(self.motion_names)))
        # assign for easy access
        self.global_trans = self.state_dict["motion_chunks", "global_trans"].to(self.motion_device)  # (N, T, B, 3)
        # adjust the rigid body height to avoid penetration
        self.global_trans[..., 2] -= (torch.clamp(self.global_trans[..., 2].min(-1, keepdim=True)[0], max=0.15) - 0.032)
        # self.global_trans[..., 2] -= (self.global_trans[..., 2].min(-1, keepdim=True)[0] - 0.032)
        self.global_rot_quat = self.state_dict["motion_chunks", "global_rot_quat"].to(self.motion_device)
        self.global_vel = self.state_dict["motion_chunks", "global_vel"].to(self.motion_device)
        self.global_ang_vel = self.state_dict["motion_chunks", "global_ang_vel"].to(self.motion_device)
        self.global_root_vel = self.state_dict["motion_chunks", "global_root_vel"].to(self.motion_device)
        self.global_root_ang_vel = self.state_dict["motion_chunks", "global_root_ang_vel"].to(self.motion_device)
        self.dof_pos = self.state_dict["motion_chunks", "dof_pos"].to(self.motion_device)
        self.dof_vel = self.state_dict["motion_chunks", "dof_vel"].to(self.motion_device)
        self.feet_contact_profile = self.state_dict["motion_chunks"]["feet_contact_profile"].to(self.motion_device)

        self.m_dt = self.state_dict["motion_specs", "motion_dt"].to(self.motion_device)
        self.m_tick_span = self.state_dict["motion_specs", "motion_tick_span"].to(self.motion_device)
        self.m_frame = self.state_dict["motion_specs", "motion_frame"].to(self.motion_device)
        m_shifted_frame = self.m_frame.clone().roll(1)
        m_shifted_frame[0] = 0
        self.m_frames_offset = m_shifted_frame.cumsum(dim=0)  # the offset of the motion start frames
        self.m_weights = self.state_dict["motion_specs", "motion_weight"].to(self.motion_device)
        self.termination_counts = torch.ones_like(self.m_weights)  # provide an initial warmup for finetuning.
        self.sampling_probs = torch.ones_like(self.m_weights) / self.num_motions
        self.m_ids = self.state_dict["motion_specs", "motion_id"].to(self.motion_device)

    def get_motion_state(self, motion_ids, start_ticks,
                         ref_height_adjustment=0.0, full_state=False) -> MotionState:
        m_tick_span = self.m_tick_span[motion_ids]
        start_tick = start_ticks.clip(min=0).clip(max=m_tick_span)
        n_frame = self.m_frame[motion_ids]
        m_dt = self.m_dt[motion_ids]

        start_frames, end_frames, blend = self._calc_frame_blend(start_tick, m_tick_span, n_frame, m_dt)
        blend = blend[..., None]  # for root or dof blending with one feature dim
        blend_exp = blend[..., None]  # for whole body blending with two feature dims

        f0idx = start_frames + self.m_frames_offset[motion_ids]
        f1idx = end_frames + self.m_frames_offset[motion_ids]

        global_trans0 = self.global_trans[f0idx]
        global_trans1 = self.global_trans[f1idx]
        global_translations = global_trans0 * (1 - blend_exp) + global_trans1 * blend_exp
        global_translations[..., 2] += ref_height_adjustment
        global_vel0 = self.global_vel[f0idx]
        global_vel1 = self.global_vel[f1idx]
        global_velocities = global_vel0 * (1 - blend_exp) + global_vel1 * blend_exp
        global_ang_vel0 = self.global_ang_vel[f0idx]
        global_ang_vel1 = self.global_ang_vel[f1idx]
        global_ang_velocities = global_ang_vel0 * (1 - blend_exp) + global_ang_vel1 * blend_exp

        dof_pos0, dof_pos1 = self.dof_pos[f0idx], self.dof_pos[f1idx]
        dof_vel0, dof_vel1 = self.dof_vel[f0idx], self.dof_vel[f1idx]
        dof_positions = dof_pos0 * (1 - blend) + dof_pos1 * blend
        dof_velocities = dof_vel0 * (1 - blend) + dof_vel1 * blend

        global_rot0, global_rot1 = self.global_rot_quat[f0idx], self.global_rot_quat[f1idx]
        global_rotations = torch_utils.slerp(global_rot0, global_rot1, blend_exp)

        if not self.scaler_last:
            global_rotations = scaler_first(global_rotations)

        feet_contact0, feet_contact1 = self.feet_contact_profile[f0idx], self.feet_contact_profile[f1idx]
        feet_contact_profile = torch.logical_and(feet_contact0, feet_contact1)

        # index the motion state
        motion_state = MotionState(
            dof_pos=dof_positions,
            dof_vel=dof_velocities,
            rb_pos=global_translations,
            rb_rot=global_rotations,
            rb_vel=global_velocities,
            rb_ang_vel=global_ang_velocities,
            feet_contact_profile=feet_contact_profile,
        )
        if full_state:
            root_states = {}
            root_pos0, root_pos1 = self.global_trans[f0idx, 0], self.global_trans[f1idx, 0]
            root_pos = root_pos0 * (1 - blend) + root_pos1 * blend
            root_pos[..., 2] += ref_height_adjustment
            root_states.update({"root_pos": root_pos})
            root_rot0, root_rot1 = self.global_rot_quat[f0idx, 0], self.global_rot_quat[f1idx, 0]
            root_rot = torch_utils.slerp(root_rot0, root_rot1, blend)
            root_states.update({"root_rot": root_rot})
            root_vel0, root_vel1 = self.global_root_vel[f0idx], self.global_root_vel[f1idx]
            root_vel = root_vel0 * (1 - blend) + root_vel1 * blend
            root_states.update({"root_vel": root_vel})
            root_ang_vel0, root_ang_vel1 = self.global_root_ang_vel[f0idx], self.global_root_ang_vel[f1idx]
            root_ang_vel = root_ang_vel0 * (1 - blend) + root_ang_vel1 * blend
            root_states.update({"root_ang_vel": root_ang_vel})
            for key, value in root_states.items():
                motion_state.set(key, value, inplace=True)
        return motion_state

    def get_motion_frames(self, motion_ids):
        return self.m_frame[motion_ids]

    def get_motion_tick_spans(self, motion_ids):
        return self.m_tick_span[motion_ids]

    @staticmethod
    def _calc_frame_blend(start_ticks, motion_tick_spans, n_frames, m_dts):
        # calculate the frame blending
        phase = torch.clip(start_ticks / motion_tick_spans, 0.0, 1.0)

        start_frames = (phase * (n_frames - 1)).long()
        end_frames = torch.min(start_frames + 1, n_frames - 1)
        blend_frames = (start_ticks / m_dts - start_frames)  # residual frame blending
        return start_frames, end_frames, blend_frames
    
    def update_sampling_probs(self, failed_ids):
        if len(failed_ids) > 0:
            self.termination_counts[failed_ids] += 1
            self.sampling_probs = self.termination_counts / self.termination_counts.sum()
        else:
            self.sampling_probs = torch.ones_like(self.m_weights) / self.num_motions
        print("======================= Dynamic Sampling =====================")
        prioritized_ids = (self.sampling_probs > 1/self.num_motions).nonzero()
        print(f"Training mostly on {len(prioritized_ids)} seqs from {self.num_motions} motions ")
        return prioritized_ids

    def sample_motions(self, n_samples, valid_mask=None):
        if valid_mask is not None:
            weights = self.state_dict["motion_specs", "motion_weight"].clone()
            weights[~valid_mask] = 0.0
        else:
            weights = self.state_dict["motion_specs", "motion_weight"]
        # sample motion ids according to the weights
        motion_ids = torch.multinomial(weights, n_samples, replacement=True)
        return motion_ids

    def sample_time(self, motion_ids, max_time=None, truncate_time=None, truncate_phase=1.0):
        phase = truncate_phase * torch.rand(motion_ids.shape, device=self.motion_device)
        motion_time_span = self.get_motion_tick_spans(motion_ids)
        if max_time is not None:
            motion_time_span = torch.clamp(motion_time_span, max_time)
        if truncate_time is not None:
            assert truncate_time >= 0.0
            motion_time_span -= truncate_time
            assert (motion_time_span >= 0.0).all()

        return phase * motion_time_span


    @property
    def num_motions(self):
        return len(self.state_dict["motion_specs", "motion_id"])


if __name__ == "__main__":
    from easydict import EasyDict

    cfg = EasyDict({"motion_base": "/home/<USER>/Documents/Phybot_rl/data/motion_base",
                    "robot_name": "phybot", "resolve_type": "InvKinematics",
                    "pre_processed": False, "target_fps": 30.0})

    test_motion_lib = MotionLib(**cfg)
    test_motion_lib.get_motion_state(0, np.array([0.2]), full_state=True)
    print(test_motion_lib.num_motions)
