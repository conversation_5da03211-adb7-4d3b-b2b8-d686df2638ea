import torch 
import numpy as np
import legged_gym.utils.rotation_conversions as tRot
import xml.etree.ElementTree as ETree
from easydict import EasyDict
import scipy.ndimage.filters as filters
import smpl_sim.poselib.core.rotation3d as pRot
from lxml.etree import XMLParser, parse, ElementTree, Element, SubElement
from io import BytesIO
import copy
from collections import OrderedDict


class HumanoidBatch:
    def __init__(self, robot_mjcf_path, device = torch.device("cpu")):
        self.mjcf_file = robot_mjcf_path
        
        parser = XMLParser(remove_blank_text=True)
        tree = parse(BytesIO(open(self.mjcf_file, "rb").read()), parser=parser,)
        self.dof_axis = []
        joints = sorted([j.attrib['name'] for j in tree.getroot().find("worldbody").findall('.//joint')])
        motors = sorted([m.attrib['name'] for m in tree.getroot().find("actuator").getchildren()])
        assert(len(motors) > 0, "No motors found in the mjcf file")
        
        self.num_dof = len(motors) 
        self.num_extend_dof = self.num_dof
        
        self.mjcf_data = mjcf_data = self.from_mjcf(self.mjcf_file)
        self.body_names = copy.deepcopy(mjcf_data['node_names'])
        self._parents = mjcf_data['parent_indices']
        self.body_names_augment = copy.deepcopy(mjcf_data['node_names'])
        self._offsets = mjcf_data['local_translation'][None, ].to(device)
        self._local_rotation = mjcf_data['local_rotation'][None, ].to(device)
        self.actuated_joints_idx = np.array([self.body_names.index(k) for k, v in mjcf_data['body_to_joint'].items()])
        
        for m in motors:
            if not m in joints:
                print(m)
        
        if "type" in tree.getroot().find("worldbody").findall('.//joint')[0].attrib and tree.getroot().find("worldbody").findall('.//joint')[0].attrib['type'] == "free":
            for j in tree.getroot().find("worldbody").findall('.//joint')[1:]:
                self.dof_axis.append([int(i) for i in j.attrib['axis'].split(" ")])
            self.has_freejoint = True
        elif not "type" in tree.getroot().find("worldbody").findall('.//joint')[0].attrib:
            for j in tree.getroot().find("worldbody").findall('.//joint'):
                self.dof_axis.append([int(i) for i in j.attrib['axis'].split(" ")])
            self.has_freejoint = True
        else:
            for j in tree.getroot().find("worldbody").findall('.//joint')[6:]:
                self.dof_axis.append([int(i) for i in j.attrib['axis'].split(" ")])
            self.has_freejoint = False
        
        self.dof_axis = torch.tensor(self.dof_axis)
            
        self.num_bodies = len(self.body_names)
        self.num_bodies_augment = len(self.body_names_augment)
        

        self.joints_range = mjcf_data['joints_range'].to(device)
        self._local_rotation_mat = tRot.quaternion_to_matrix(self._local_rotation).float() # w, x, y ,z
        # self.load_mesh()
        
    def from_mjcf(self, path):
        # function from Poselib: 
        tree = ETree.parse(path)
        xml_doc_root = tree.getroot()
        xml_world_body = xml_doc_root.find("worldbody")
        if xml_world_body is None:
            raise ValueError("MJCF parsed incorrectly please verify it.")
        # assume this is the root
        xml_body_root = xml_world_body.find("body")
        if xml_body_root is None:
            raise ValueError("MJCF parsed incorrectly please verify it.")
            
        xml_joint_root = xml_body_root.find("joint")
        
        node_names = []
        parent_indices = []
        local_translation = []
        local_rotation = []
        joints_range = []
        body_to_joint = OrderedDict()

        # recursively adding all nodes into the skel_tree
        def _add_xml_node(xml_node, parent_index, node_index):
            node_name = xml_node.attrib.get("name")
            # parse the local translation into float list
            pos = np.fromstring(xml_node.attrib.get("pos", "0 0 0"), dtype=float, sep=" ")
            quat = np.fromstring(xml_node.attrib.get("quat", "1 0 0 0"), dtype=float, sep=" ")
            node_names.append(node_name)
            parent_indices.append(parent_index)
            local_translation.append(pos)
            local_rotation.append(quat)
            curr_index = node_index
            node_index += 1
            all_joints = xml_node.findall("joint") # joints need to remove the first 6 joints
            if len(all_joints) == 6:
                all_joints = all_joints[6:]
            
            for joint in all_joints:
                if not joint.attrib.get("range") is None: 
                    joints_range.append(np.fromstring(joint.attrib.get("range"), dtype=float, sep=" "))
                else:
                    if not joint.attrib.get("type") == "free":
                        joints_range.append([-np.pi, np.pi])
            for joint_node in xml_node.findall("joint"):
                body_to_joint[node_name] = joint_node.attrib.get("name")
                
            for next_node in xml_node.findall("body"):
                node_index = _add_xml_node(next_node, curr_index, node_index)

            
            return node_index
        
        _add_xml_node(xml_body_root, -1, 0)
        assert(len(joints_range) == self.num_dof) 
        return {
            "node_names": node_names,
            "parent_indices": torch.from_numpy(np.array(parent_indices, dtype=np.int32)),
            "local_translation": torch.from_numpy(np.array(local_translation, dtype=np.float32)),
            "local_rotation": torch.from_numpy(np.array(local_rotation, dtype=np.float32)),
            "joints_range": torch.from_numpy(np.array(joints_range)),
            "body_to_joint": body_to_joint
        }
        
    def fk_batch(self, root_trans, pose_aa=None,
                dof_pos=None, wbody_pos=None, wbody_mat=None,
                 bypass=False, return_full=False, fps=30):
        """The entry point for the forward kinematics function.
        Arguments :
        -- pose: (B, seq_len, J, 3) tensor of local rotations in axis-angle format.
        -- trans: (B, seq_len, 3) tensor of global root translations.
        -- convert_to_mat: If True, convert the input pose to rotation matrices before performing forward kinematics.
        -- return_full: If True, return all the computed values. If False, return only the global translation and rotation.
        -- dt: The time step between each frame in the input pose.
        Output: A dictionary containing the following keys:
        partial:
        -- global_translation: (B, seq_len, J, 3) tensor of global joint positions.
        -- global_rotation: (B, seq_len, J, 4) tensor of global joint rotations in quaternion format, where the last element is the scalar part.
        -- global_rotation_mat: (B, seq_len, J, 3, 3) tensor of global joint rotations in matrix format.
        full_extra:
        -- global_velocity: (B, seq_len, J, 3) tensor of global joint velocities.
        -- global_angular_velocity: (B, seq_len, J, 3) tensor of global joint angular velocities.
        -- global_root_velocity: (B, seq_len, 3) tensor of global root velocities.
        -- global_root_angular_velocity: (B, seq_len, 3) tensor of global root angular velocities.

        -- local_rotation: (B, seq_len, J, 4) tensor of local rotations in quaternion format w.r.t the parent joint, where the last element is the scalar part.
        -- dof_pos: (B, seq_len, J) tensor of the sum of the local rotations in axis-angle format.
        -- dof_vels: (B, seq_len, J) tensor of the sum of the local angular velocities in axis-angle format.

        -- fps: The frame rate of the input pose.
         """
        B, seq_len = root_trans.shape[:2]
        return_dict = EasyDict()
        if not bypass:
            assert pose_aa is not None, "pose_aa must be provided if bypass is False."
            pose_aa = pose_aa[..., :len(self._parents), :]
            pose_quat = tRot.axis_angle_to_quaternion(pose_aa.clone())
            pose_mat = tRot.quaternion_to_matrix(pose_quat)
            if pose_mat.shape != 5:
                pose_mat = pose_mat.reshape(B, seq_len, -1, 3, 3)
            wbody_pos, wbody_mat = self.forward_kinematics_batch(pose_mat[:, :, 1:], pose_mat[:, :, 0:1], root_trans)
            if not len(self.actuated_joints_idx) == len(self.body_names):
                return_dict.dof_pos = pose_aa.sum(dim=-1)[..., self.actuated_joints_idx]
            else:
                return_dict.dof_pos = pose_aa.sum(dim=-1)[..., 1:]
        else:
            assert wbody_pos is not None and wbody_mat is not None, "wbody_pos and wbody_mat must be provided if bypass is True."
            return_dict.dof_pos = dof_pos

        wbody_rot = tRot.wxyz_to_xyzw(tRot.matrix_to_quaternion(wbody_mat))
        return_dict.global_trans = wbody_pos
        # return_dict.global_rot_mat = wbody_mat
        return_dict.global_rot_quat = wbody_rot

        if return_full:
            return_dict.update(self.compute_velocity(wbody_pos, wbody_rot,
                                                     return_dict.dof_pos.clone(), fps))

        return return_dict

    def compute_velocity(self, wbody_pos, wbody_rot, dof_pos, fps=30):
        return_dict = EasyDict()
        B, seq_len = wbody_pos.shape[:2]
        dt = 1.0/fps
        rigidbody_linear_velocity = self._compute_velocity(wbody_pos, dt)
        rigidbody_angular_velocity = self._compute_angular_velocity(wbody_rot, dt)
        return_dict.global_root_vel = rigidbody_linear_velocity[..., 0, :]
        return_dict.global_root_ang_vel = rigidbody_angular_velocity[..., 0, :]
        return_dict.global_ang_vel = rigidbody_angular_velocity
        return_dict.global_vel = rigidbody_linear_velocity

        dof_vel = ((dof_pos[:, 1:] - dof_pos[:, :-1]) / dt)
        return_dict.dof_vel = torch.cat([dof_vel, dof_vel[:, -2:-1]], dim=1)
        return_dict.fps = fps * torch.ones((B, seq_len))
        return return_dict
    
    def forward_kinematics_batch(self, rotations, root_rotations, root_positions):
        """
        Perform forward kinematics using the given trajectory and local rotations.
        Arguments (where B = batch size, J = number of joints):
         -- rotations: (B, J, 4) tensor of unit quaternions describing the local rotations of each joint.
         -- root_positions: (B, 3) tensor describing the root joint positions.
        Output: joint positions (B, J, 3)
        """
        
        device, dtype = root_rotations.device, root_rotations.dtype
        B, seq_len = rotations.size()[0:2]
        J = self._offsets.shape[1]
        positions_world = []
        rotations_world = []

        expanded_offsets = (self._offsets[:, None].expand(B, seq_len, J, 3).to(device).type(dtype))
        # print(expanded_offsets.shape, J)
        for i in range(J):
            if self._parents[i] == -1:
                positions_world.append(root_positions)
                rotations_world.append(root_rotations)
            else:
                jpos = (torch.matmul(rotations_world[self._parents[i]][:, :, 0], expanded_offsets[:, :, i, :, None]).squeeze(-1) + positions_world[self._parents[i]])
                rot_mat = torch.matmul(rotations_world[self._parents[i]], torch.matmul(self._local_rotation_mat[:,  (i):(i + 1)], rotations[:, :, (i - 1):i, :]))
                # rot_mat = torch.matmul(rotations_world[self._parents[i]], rotations[:, :, (i - 1):i, :])
                # print(rotations[:, :, (i - 1):i, :].shape, self._local_rotation_mat.shape)
                
                positions_world.append(jpos)
                rotations_world.append(rot_mat)
        
        positions_world = torch.stack(positions_world, dim=2)
        rotations_world = torch.cat(rotations_world, dim=2)
        return positions_world, rotations_world
    
    @staticmethod
    def _compute_velocity(p, time_delta, guassian_filter=True):
        velocity = np.gradient(p.numpy(), axis=-3) / time_delta
        if guassian_filter:
            velocity = torch.from_numpy(filters.gaussian_filter1d(velocity, 2, axis=-3, mode="nearest")).to(p)
        else:
            velocity = torch.from_numpy(velocity).to(p)
        
        return velocity
    
    @staticmethod
    def _compute_angular_velocity(r, time_delta: float, guassian_filter=True):
        # assume the second last dimension is the time axis
        diff_quat_data = pRot.quat_identity_like(r).to(r)
        diff_quat_data[..., :-1, :, :] = pRot.quat_mul_norm(r[..., 1:, :, :], pRot.quat_inverse(r[..., :-1, :, :]))
        diff_angle, diff_axis = pRot.quat_angle_axis(diff_quat_data)
        angular_velocity = diff_axis * diff_angle.unsqueeze(-1) / time_delta
        if guassian_filter:
            angular_velocity = torch.from_numpy(filters.gaussian_filter1d(angular_velocity.numpy(), 2, axis=-3, mode="nearest"),)
        return angular_velocity  
