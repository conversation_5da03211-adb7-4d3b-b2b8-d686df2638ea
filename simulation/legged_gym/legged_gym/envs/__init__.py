# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2021 ETH Zurich, Nikita Rudin

from legged_gym import LEGGED_GYM_ROOT_DIR, LEGGED_GYM_ENVS_DIR
from .base.legged_robot import LeggedRobot

from .base.humanoid import Humanoid

from .gr1.gr1_walk_phase import GR1WalkPhase
from .gr1.gr1_walk_phase_config import GR1WalkPhaseCfg, GR1WalkPhaseCfgPPO

from .g1.g1_walk_phase import G1WalkPhase
from .g1.g1_walk_phase_config import G1WalkPhaseCfg, G1WalkPhaseCfgPPO

from .h1.h1_walk_phase import H1WalkPhase
from .h1.h1_walk_phase_config import H1WalkPhaseCfg, H1WalkPhaseCfgPPO

from .berkeley.berkeley_walk_phase import BerkeleyWalkPhase
from .berkeley.berkeley_walk_phase_config import BerkeleyWalkPhaseCfg, BerkeleyWalkPhaseCfgPPO

from .phybot.phybot_mark2_walk_phase import PM2WalkPhase
from .phybot.phybot_mark2_walk_phase_config import PM2WalkPhaseCfg, PM2WalkPhaseCfgPPO

from .phybot.phybot_mark2_mimic import PM2Mimic
from .phybot.phybot_mark2_mimic_config import PM2MimicCfg, PM2MimicCfgPPO

from .phybot.phybot_mark2_mimic_sparse import PM2MimicSparse
from .phybot.phybot_mark2_mimic_sparse_config import PM2MimicSparseCfg, PM2MimicSparseCfgPPO

from .phybot.phybot_mark2_dmp import PM2DMP
from .phybot.phybot_mark2_dmp_config import PM2DMPCfg, PM2DMPCfgPPO

from .phybot.phybot_mark2_mimic_proprio import PM2MimicProprio
from .phybot.phybot_mark2_mimic_proprio_config import PM2MimicProprioCfg, PM2MimicProprioCfgPPO

from .phybot.phybot_mark2_multimodal_mimic import PM2MultimodalMimic
from .phybot.phybot_mark2_multimodal_mimic_config import PM2MultimodalMimicCfg, PM2MultimodalMimicCfgPPO

from .phybot_mini.phybot_mini_walk_commands import PMMiniWalkCommands
from .phybot_mini.phybot_mini_walk_commands_config import PMMiniWalkCommandsCfg, PMMiniWalkCommandsCfgPPO

from .phybot_mini.phybot_mini_mimic import PMMiniMimic
from .phybot_mini.phybot_mini_mimic_config import PMMiniMimicCfg, PMMiniMimicCfgPPO

from .phybot_mini.phybot_mini_mimic_proprio import PMMiniMimicProprio
from .phybot_mini.phybot_mini_mimic_proprio_config import PMMiniMimicProprioCfg, PMMiniMimicProprioCfgPPO

from legged_gym.gym_utils.task_registry import task_registry

# ======================= environment registration =======================

task_registry.register("gr1_walk_phase", GR1WalkPhase, GR1WalkPhaseCfg(), GR1WalkPhaseCfgPPO())

task_registry.register("g1_walk_phase", G1WalkPhase, G1WalkPhaseCfg(), G1WalkPhaseCfgPPO())

task_registry.register("h1_walk_phase", H1WalkPhase, H1WalkPhaseCfg(), H1WalkPhaseCfgPPO())

task_registry.register("berkeley_walk_phase", BerkeleyWalkPhase, BerkeleyWalkPhaseCfg(), BerkeleyWalkPhaseCfgPPO())

task_registry.register("phybot_mark2_walk_phase", PM2WalkPhase, PM2WalkPhaseCfg(), PM2WalkPhaseCfgPPO())

task_registry.register("phybot_mark2_mimic", PM2Mimic, PM2MimicCfg(), PM2MimicCfgPPO())

task_registry.register("phybot_mark2_mimic_sparse", PM2MimicSparse, PM2MimicSparseCfg(), PM2MimicSparseCfgPPO())

task_registry.register("phybot_mark2_dmp", PM2DMP, PM2DMPCfg(), PM2DMPCfgPPO())

task_registry.register("phybot_mark2_mimic_proprio", PM2MimicProprio, PM2MimicProprioCfg(), PM2MimicProprioCfgPPO())

task_registry.register("phybot_mark2_multimodal_mimic", PM2MultimodalMimic, PM2MultimodalMimicCfg(), PM2MultimodalMimicCfgPPO())

task_registry.register("phybot_mini_walk_commands", PMMiniWalkCommands, PMMiniWalkCommandsCfg(), PMMiniWalkCommandsCfgPPO())

task_registry.register("phybot_mini_mimic", PMMiniMimic, PMMiniMimicCfg(), PMMiniMimicCfgPPO())

task_registry.register("phybot_mini_mimic_proprio", PMMiniMimicProprio, PMMiniMimicProprioCfg(), PMMiniMimicProprioCfgPPO())
