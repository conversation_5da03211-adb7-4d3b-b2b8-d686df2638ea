from legged_gym.envs.base.humanoid_config import HumanoidCfg, HumanoidCfgPPO


class PMMiniMimicCfg(HumanoidCfg):
    class env(HumanoidCfg.env):
        num_envs = 8192
        num_actions = num_dofs = 21
        n_key_bodies = 10
        n_proprio = 4 + 3 + 3 + 3 * num_actions
        n_priv = 1 + 2 + 3 + n_key_bodies * 3
        n_priv_latent = 4 + 1 + 2 * num_dofs # 4 + 1 + 3 + 2 * num_dofs  
        
        num_future_steps = 3
        num_interval_steps = 1
        num_task_obs_inst = (1 + 6 + 3 + 3 + num_dofs + 2 * n_key_bodies * 3)
        num_task_observations = num_task_obs_inst * num_future_steps
        history_len = 3
        num_observations = (n_proprio + num_task_observations + n_priv + n_priv_latent + 
                            history_len * n_proprio)

        num_privileged_obs = None

        env_spacing = 3.  # not used with heightfields/trimeshes
        send_timeouts = True  # send time out information to the algorithm
        episode_length_s = 10

        randomize_start_pos = True
        randomize_start_yaw = True

        history_encoding = True
        contact_buf_len = 10

        normalize_obs = True
        relative_to_root = True

        enable_height_termination = True
        enable_orient_termination = True
        enable_max_kb_pos_err_cut = True

        termination_dist_curriculum = True
        init_termination_dist = 1.25
        dist_curriculum_scale = -1.0

        height_offset = 0.02

        # enable_root_tracking = True
        # enable_upper_tracking = True
        # enable_lower_tracking = True


    class motion_lib:
        motion_sync = False
        motion_base = "{LEGGED_GYM_ROOT_DIR}/motion_base"
        motion_dump = "mini_walks_120fps"  # kick_punch_120fps, walk_and_runs_sub_120fps, walks_120fps_contact, cmu_no_jump_run_120fps, cmu_jump132_120fps
        pre_processed = False
        specified_ids = ["02_01_poses", ] # "motions_passed_5000.yaml"  # 02_04_poses, 91_62_poses, 120_14_poses, 140_07_poses
        # specified_ids = None
        robot_name = "phybot_mini"
        num_to_load = -1
        sample_scheme = "dynamic"  # preceding, dynamic, uniform
        start_init_prob = 0
        init_random_prob = 0
        init_default_prob = 0.15
        truncate_phase = 0.85
        cyclic_transition = True
        device = "cuda:0"
        rigid_body_names = [
            'base_link',
            'left_hip_pitch', 'left_hip_roll', 'left_hip_yaw', 'left_knee', 'left_ankle_pitch', 'left_ankle_roll',
            "right_hip_pitch", "right_hip_roll", "right_hip_yaw", "right_knee", "right_ankle_pitch", "right_ankle_roll",
            'waist_yaw', 'neck_yaw', 'neck_pitch',
            "left_shoulder_pitch", "left_shoulder_roll", "left_shoulder_yaw", "left_elbow_pitch",
            "right_shoulder_pitch", "right_shoulder_roll", "right_shoulder_yaw", "right_elbow_pitch",
        ]
        feet_idx = [6, 12] # heels/ankle_rolls
        global_tracking = True
        global_trans_reset_time = 0.5
        enable_sparse_keyframe = False

        min_bucket_weight = 1e-3
        max_bucket_weight = 40
        bucket_width = 2  # count is second (0.2/10 clips)
        weight_refresh_epoch = 10

        collect_rollouts = False  # whether to collect rollouts for each motion
        rollout_cycle_times = 1

    class terrain(HumanoidCfg.terrain):
        mesh_type = 'plane'
        height = [0, 0.04]
        horizontal_scale = 0.1

    class init_state(HumanoidCfg.init_state):
        mode = "Mocap"  # Mocap, Default
        pos = [0, 0, 0.68]
        default_joint_angles = {
            'left_hip_pitch': -0.15,
            'left_hip_roll': 0.0,
            'left_hip_yaw': 0.0,
            'left_knee': 0.3,
            'left_ankle_pitch': -0.15,
            'left_ankle_roll': 0.0,

            'right_hip_pitch': -0.15,
            'right_hip_roll': 0.0,
            'right_hip_yaw': 0.0,
            'right_knee': 0.3,
            'right_ankle_pitch': -0.15,
            'right_ankle_roll': 0.0,

            'waist_yaw': 0.0,

            'left_shoulder_pitch': 0.0,
            'left_shoulder_roll': 0.0,
            'left_shoulder_yaw': 0.0,
            'left_elbow_pitch': 0.0,

            'right_shoulder_pitch': 0.0,
            'right_shoulder_roll': 0.0,
            'right_shoulder_yaw': 0.0,
            'right_elbow_pitch': 0.0,
        }

    class normalization(HumanoidCfg.normalization):
        clip_actions = 10  # "P": 10, "residual_pos": 5

    class control(HumanoidCfg.control):
        control_type = 'P'  # "P", "residual_pos", "Idle"
        reference_type = "mocap"
        # stiffness = {'hip_pitch': 150,
        #              'hip_roll': 150,
        #              'hip_yaw': 150,
        #              'knee': 150,
        #              'ankle_pitch': 100,
        #              "ankle_roll": 30,
        #              'waist_yaw': 100,
        #              'shoulder_pitch': 100,
        #              'shoulder_roll': 100,
        #              'shoulder_yaw': 30,
        #              'elbow_pitch': 100,
        #              }  # [N*m/rad]
        # damping = {'hip_pitch': 10,
        #            'hip_roll': 10,
        #            'hip_yaw': 10,
        #            'knee': 10,
        #            'ankle_pitch': 20,
        #            "ankle_roll": 4,
        #            'waist_yaw': 10.,
        #            'shoulder_pitch': 10,
        #            'shoulder_roll': 10,
        #            'shoulder_yaw': 4,
        #            'elbow_pitch': 10,
        #            }  # [N*m/rad]  # [N*m*s/rad]

        stiffness = {'hip_pitch': 100,
                     'hip_roll': 100,
                     'hip_yaw': 100,
                     'knee': 100,
                     'ankle_pitch': 100,
                     "ankle_roll": 30,
                     'waist_yaw': 100,
                     'shoulder_pitch': 100,
                     'shoulder_roll': 100,
                     'shoulder_yaw': 30,
                     'elbow_pitch': 100,
                     }  # [N*m/rad]
        damping = {'hip_pitch': 20,
                   'hip_roll': 20,
                   'hip_yaw': 20,
                   'knee': 20,
                   'ankle_pitch': 20,
                   "ankle_roll": 4,
                   'waist_yaw': 20.,
                   'shoulder_pitch': 20,
                   'shoulder_roll': 20,
                   'shoulder_yaw': 4,
                   'elbow_pitch': 20,
                   }  # [N*m/rad]  # [N*m*s/rad]

        action_scale = 0.25
        decimation = 10

    class sim(HumanoidCfg.sim):
        dt = 0.001

    class asset(HumanoidCfg.asset):
        file = '{LEGGED_GYM_ROOT_DIR}/resources/robots/phybot_mini/phybot_mini_mark1.urdf'
        # for both joint and link name
        torso_name: str = 'base_link'  # humanoid pelvis part
        chest_name: str = 'waist_yaw'  # humanoid chest part
        forehead_name: str = 'neck_pitch'  # humanoid head part

        waist_name: str = 'waist'

        # for link name
        thigh_name: str = 'hip_yaw'
        shank_name: str = 'knee'
        foot_name: str = 'ankle_roll'  # foot_pitch is not used
        upper_arm_name: str = 'shoulder_roll'
        lower_arm_name: str = 'elbow_pitch'
        hand_name: str = 'wrist'

        # for joint name
        hip_name: str = 'hip'
        hip_roll_name: str = 'hip_roll'
        hip_yaw_name: str = 'hip_yaw'
        hip_pitch_name: str = 'hip_pitch'
        knee_name: str = 'knee'
        ankle_name: str = 'ankle'
        ankle_pitch_name: str = 'ankle_pitch'
        shoulder_name: str = 'shoulder'
        shoulder_pitch_name: str = 'shoulder_pitch'
        shoulder_roll_name: str = 'shoulder_roll'
        shoulder_yaw_name: str = 'shoulder_yaw'
        elbow_name: str = 'elbow_pitch'

        feet_bodies = ['left_ankle_roll', 'right_ankle_roll']
        n_lower_body_dofs: int = 12

        key_body_names = [
            "base_link", "waist_yaw",
            "left_knee", "left_ankle_roll",
            "right_knee", "right_ankle_roll",
            "left_shoulder_roll", "left_elbow_pitch", 
            "right_shoulder_roll", "right_elbow_pitch",
        ]
        whole_body_groups = ["left_hip", "left_knee", "left_ankle", 
                             "right_hip", "right_knee", "right_ankle",
                              "waist",
                              "left_shoulder", "left_elbow",
                              "right_shoulder", "right_elbow",
                            ]

        penalize_contacts_on = ["shoulder", "elbow", "hip"]
        terminate_after_contacts_on = ['base_link', "left_knee", "right_knee", "left_elbow_yaw", "right_elbow_yaw", ]
        non_terminate_body_names = ["left_ankle_roll", "right_ankle_roll"]

    class rewards(HumanoidCfg.rewards):
        regularization_names = [
            "dof_error",
            "dof_error_upper",
            "feet_stumble",
            "feet_contact_forces",
            "foot_slip",
            "lin_vel_z",
            "ang_vel_xy",
            "orientation",
            "dof_pos_limits",
            "dof_torque_limits",
            "collision",
            "torque_penalty",
            "no_fly",
            "action_rate",
            "energy",
            "dof_acc",
            "dof_vel",
            "delta_torques",
        ]
        regularization_scale = 1.0
        regularization_scale_range = [0.8, 2.0]
        regularization_scale_curriculum = True
        regularization_scale_gamma = 0.0001

        tracking_metrics = ["mpjpe", "gmpjpe", "max_gmpjpe"]
        reward_multiplier = 1.0
        num_lower_joints = 12
        
        joint_weights = [
            1.0, 1.0, 1.0, 1.0, 0.5, 0.5,
            1.0, 1.0, 1.0, 1.0, 0.5, 0.5,
            1.0,
            1.0, 1.0, 1.0, 1.0,
            1.0, 1.0, 1.0, 1.0,
        ]
        keybody_weights = [
            1.0, 1.0, 
            1.0, 0.5, 1.0, 0.5,
            1.0, 1.0, 1.0, 1.0,
        ]

        class scales:
            #### task rewards ####
            alive = 1.0
            # configuration space task rew
            tracking_ref_dof_pos = 5.0
            tracking_ref_dof_vel = 3.0
            
            # carterian space task rew
            tracking_ref_kb_pos = 5.0
            tracking_ref_kb_rot = 2.0
            tracking_ref_kb_lin_vel = 3.0
            tracking_ref_kb_ang_vel = 1.0
            tracking_feet_contacts = 2.0
            
            # base/root rew
            tracking_ref_lin_vel = 3.0  # [3.0, 5.0]
            tracking_ref_ang_vel = 1.0 # [1.0, 2.0]
            tracking_ref_height = 2.0 # [2.0, 3.0]
            # tracking_ref_yaw = 1.0  # [1.0, 2.0]
            # tracking_ref_roll_pitch = 1.0 # [1.0, 2.0]
            
            # air-time walk style rew
            # # single_support = 1.5
            # feet_air_time = 20.0
            # feet_height = 5.0
            # feet_distance = 1.0
            # feet_orientation = -2.0
            # tracking_feet_contacts = 2.0
            # dof_error_upper = -1.0
            # # dof_error = -0.15

            # base/root reg
            # # lin_vel_z = -0.5
            # ang_vel_xy = -0.05
            # orientation = -10

            # limbs reg
            feet_stumble = -100
            feet_contact_forces = -1e-2
            foot_slip = -2
            collision = -100.0
            no_fly = -5

            # control reg
            dof_pos_limits = -10
            action_rate = -0.2
            energy = -1e-3
            dof_torque_limits = -2.0
            dof_acc = -1e-7
            dof_vel = -1e-3
            delta_torques = -1e-5
            torque_penalty = -1e-4

            # # control reg hard
            # dof_pos_limits = -50
            # action_rate = -0.3
            # energy = -3e-3
            # dof_torque_limits = -5.0
            # dof_acc = -3e-7
            # dof_vel = -3e-3
            # delta_torques = -3e-5
            # torque_penalty = -3e-4

        min_dist = 0.2
        max_dist = 0.3
        target_feet_height = 0.10
        max_contact_force = 250

    class domain_rand:
        domain_rand_general = True  # manually set this, setting from parser does not work;

        # push_robots = (False and domain_rand_general)
        push_robots = False
        push_interval_s = 3
        max_push_vel_xy = 1.0
        max_push_ang_vel = 0.5

        randomize_motor = True
        motor_strength_range = [0.8, 1.2] # [0.8, 1.2]

        action_delay = True
        action_buf_len = 8

        # apply the dynamics randomization follow versatile_loco: https://arxiv.org/abs/2401.16889
        randomize_friction = (True and domain_rand_general)
        friction_range = [0.2, 1.5]

        # base link 
        randomize_base_mass = (True and domain_rand_general)
        added_mass_range = [-3., 3]
        randomize_base_com = (True and domain_rand_general)
        added_com_range = [-0.06, 0.06] 

        randomize_joint_friction = (True and domain_rand_general)
        joint_friction_range = [0.005, 0.02]
        # randomize_joint_damping = (True and domain_rand_general)
        # joint_damping_range = [0.1, 1.2]
        randomize_joint_armature = (True and domain_rand_general)
        joint_armature_range = [0.008, 0.02]  # target value

        randomize_pd_gains = (False and domain_rand_general)
        p_gain_range = [0.8, 1.2]
        d_gain_range = [0.8, 1.2]

        randomize_gravity = (True and domain_rand_general)
        gravity_rand_interval_s = 10
        gravity_range = (-0.1, 0.1)

    class noise(HumanoidCfg.noise):
        add_noise = False
        noise_increasing_steps = 5000

        class noise_scales:
            dof_pos = 0.01
            dof_vel = 0.1
            lin_vel = 0.1
            ang_vel = 0.05
            gravity = 0.05
            imu = 0.05

    class commands:
        curriculum = False
        resample = False
        num_commands = 3
        tracking_modes = ["Root", "WholeBody"]  # ["Root", "Telep", "WholeBody"]
        resampling_time = 3.  # time before command are changed[s]

        ang_vel_clip = 0.1
        lin_vel_clip = 0.1

        class ranges:
            lin_vel_x = [0., 1.6]  # min max [m/s]
            lin_vel_y = [-0.3, 0.3]
            ang_vel_yaw = [-0.6, 0.6]  # min max [rad/s]


class PMMiniMimicCfgPPO(HumanoidCfgPPO):
    seed = 1

    class runner(HumanoidCfgPPO.runner):
        policy_class_name = "ActorCritic" # ["ActorCritic", 'ActorCriticRMA', "McpActorCritic"]
        algorithm_class_name = 'PPO'  #  "PPORMA"
        runner_class_name = 'OnPolicyRunner'
        max_iterations = 8001  # number of policy updates

        # logging
        save_interval = 100  # check for potential saves every this many iterations
        eval_interval = -1  # -1: disable, 1: eval only, 500
        experiment_name = 'test'
        run_name = ''
        # load and resume for learner
        resume = False
        load_run = -1  # -1 = last run
        checkpoint = -1  # -1 = last saved model
        resume_path = None  # updated from load_run and chkpt
    
    class policy(HumanoidCfgPPO.policy):
        teacher_policy = True
        actor_hidden_dims = [1024, 512, 256, ]  # [1024, 512, 256, ], [512, 256, 128, ]
        critic_hidden_dims = [1024, 512, 256, ]
        action_std = [0.3, 0.3, 0.3, 0.4, 0.2, 0.2, 0.1] * 2 + [0.1, 0.1] + [0.1] * 10

        # MCP-specific configs
        num_prims = 4
        ensemble_type = "mcp"
        composer_hidden_dims = [1024, 512, 256,]

    class expert_policy(HumanoidCfgPPO.policy):
        teacher_policy = True
        actor_hidden_dims = [512, 256, 128, ]  # [1024, 512, 256, ]
        critic_hidden_dims = [1024, 512, 256, ]
        action_std = [0.3, 0.3, 0.3, 0.4, 0.2, 0.2, 0.1] * 2 + [0.1, 0.1] + [0.1] * 10

        # MCP-specific configs
        num_prims = 4
        ensemble_type = "mcp"
        composer_hidden_dims = [1024, 512, 256,]

    class algorithm(HumanoidCfgPPO.algorithm):
        # grad_penalty_coef_schedual = [0.001, 0.002, 500, 1000]  # specify None to disable it.
        grad_penalty_coef_schedual = None