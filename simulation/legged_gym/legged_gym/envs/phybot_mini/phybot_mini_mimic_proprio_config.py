from legged_gym.envs.base.humanoid_config import HumanoidCfg, HumanoidCfgPPO
from legged_gym.envs.phybot_mini.phybot_mini_mimic_config import PMMiniMimicCfg


class PMMiniMimicProprioCfg(PMMiniMimicCfg):
    class env(PMMiniMimicCfg.env):
        num_envs = 4096
        num_actions = num_dofs = 21
        n_key_bodies = 10
        n_proprio = 4 + 3 + 3 + 3 * num_actions  # 73
        n_priv = 1 + 2 + 3 + n_key_bodies * 3
        n_priv_latent = 4 + 1 + 2 * num_dofs # 4 + 1 + 3 + 2 * num_dofs  
        
        num_future_steps = 3
        num_interval_steps = 1
        num_task_obs_inst = (1 + 6 + 3 + 3 + num_dofs + 2 * n_key_bodies * 3)  # 94
        num_task_observations = num_task_obs_inst * num_future_steps
        history_len = long_history_len = 20
        short_history_len = 5
        expert_history_len = 3
        num_observations = (n_proprio + num_task_observations + long_history_len * n_proprio)
        # for the critic and expert policy
        num_privileged_obs = (n_proprio + num_task_observations + n_priv + n_priv_latent + 
                            expert_history_len * n_proprio)

        env_spacing = 3.  # not used with heightfields/trimeshes
        send_timeouts = True  # send time out information to the algorithm
        episode_length_s = 10

        randomize_start_pos = True
        randomize_start_yaw = True

        history_encoding = True
        contact_buf_len = 10

        normalize_obs = True
        relative_to_root = True  # for expert only

        enable_height_termination = True
        enable_orient_termination = True
        enable_max_kb_pos_err_cut = True

        termination_dist_curriculum = True
        init_termination_dist = 1.25
        dist_curriculum_scale = -1.0

        height_offset = 0.02
    
    class motion_lib(PMMiniMimicCfg.motion_lib):
        motion_sync = False
        motion_dump = "mini_walks_120fps"
        pre_processed = False
        specified_ids = ["02_01_poses" ]  # 02_04_poses, 91_62_poses "140_07_poses", "02_01_poses", "82_13_poses", 
        # specified_ids = "motions_passed_teacher.yaml"
        # specified_ids = ["squat01_poses", "squat02_poses", "squat03_poses", "squat04_poses", "squat05_poses"]
        num_to_load = -1
        sample_scheme = "dynamic" # preceding, dynamic, uniform
        start_init_prob = 0.0
        init_random_prob = 0
        init_default_prob = 0.15
        cyclic_transition = True
        
        global_trans_reset_time = 0.5
        bucket_width = 2
    
    class sim(HumanoidCfg.sim):
        dt = 0.002

    class control(PMMiniMimicCfg.control):
        stiffness = {'hip_pitch': 50,
                     'hip_roll': 50,
                     'hip_yaw': 50,
                     'knee': 50,
                     'ankle_pitch': 50,
                     "ankle_roll": 15,
                     'waist_yaw': 50,
                     'shoulder_pitch': 50,
                     'shoulder_roll': 50,
                     'shoulder_yaw': 15,
                     'elbow_pitch': 50,
                     }  # [N*m/rad]
        damping = {'hip_pitch': 10,
                   'hip_roll': 10,
                   'hip_yaw': 10,
                   'knee': 10,
                   'ankle_pitch': 10,
                   "ankle_roll": 5,
                   'waist_yaw': 10.,
                   'shoulder_pitch': 10,
                   'shoulder_roll': 10,
                   'shoulder_yaw': 5,
                   'elbow_pitch': 10,
                   }  # [N*m/rad]  # [N*m*s/rad]

        action_scale = 0.3
        decimation = 10

    class rewards(PMMiniMimicCfg.rewards):
        regularization_scale = 1.0
        regularization_scale_range = [1.0, 2.0]
        regularization_scale_curriculum = True
        regularization_scale_gamma = 0.0001

        joint_weights = [
            1.0, 1.0, 1.0, 1.0, 0.5, 0.5,
            1.0, 1.0, 1.0, 1.0, 0.5, 0.5,
            1.0,
            1.0, 1.0, 1.0, 1.0,
            1.0, 1.0, 1.0, 1.0,
        ]

        keybody_weights = [
            1.0, 1.0, 
            1.0, 0.5, 1.0, 0.5,
            1.0, 1.0, 1.0, 1.0,
        ]

        class scales:
            # task rew
            alive = 1.0
            tracking_ref_dof_pos = 5.0
            tracking_ref_dof_vel = 3.0
            
            tracking_ref_kb_pos = 5.0  # 5.0
            tracking_ref_kb_rot = 2.0
            tracking_ref_lin_vel = 2.0
            tracking_ref_ang_vel = 1.0
            tracking_feet_contacts = 2.0

            tracking_ref_kb_lin_vel = 3.0  # 3.0
            tracking_ref_kb_ang_vel = 1.0
            tracking_ref_height = 2.0

            # limbs reg
            feet_stumble = -100
            feet_contact_forces = -1e-2
            foot_slip = -2
            collision = -100.0
            no_fly = -5
            # dof_error = -0.15
            # dof_error_upper = -0.25
            # dof_error_waist = -0.25

            # base/root reg
            # lin_vel_z = -0.5
            # ang_vel_xy = -0.05
            # orientation = -0.6

            # control reg level1
            # dof_pos_limits = -10
            # action_rate = -0.2
            # energy = -1e-3
            # dof_torque_limits = -2.0
            # dof_acc = -1e-7
            # dof_vel = -1e-3
            # delta_torques = -1e-5
            # torque_penalty = -1e-4

            # control reg level2
            # dof_pos_limits = -100
            # action_rate = -0.3
            # energy = -1e-3
            # dof_torque_limits = -2.0
            # dof_acc = -3e-7
            # dof_vel = -3e-3
            # delta_torques = -1e-4
            # torque_penalty = -3e-4

            # control reg levle3
            dof_pos_limits = -100
            action_rate = -0.6
            # energy = -2e-3
            dof_torque_limits = -2.0
            dof_acc = -3e-5  # -6e-7
            dof_vel = -3e-2
            # delta_torques = -2e-4
            torque_penalty = -6e-4
        
        min_dist = 0.2
        max_dist = 0.3
        target_feet_height = 0.10
        max_contact_force = 250
    
    class domain_rand:
        domain_rand_general = True  # manually set this, setting from parser does not work;

        # push_robots = (False and domain_rand_general)
        push_robots = False
        push_interval_s = 3
        max_push_vel_xy = 0.8
        max_push_ang_vel = 0.5

        randomize_motor = True
        motor_strength_range = [0.8, 1.2] # [0.8, 1.2]

        action_delay = True
        action_buf_len = 8

        # apply the dynamics randomization follow versatile_loco: https://arxiv.org/abs/2401.16889
        randomize_friction = (True and domain_rand_general)
        friction_range = [0.2, 1.0]
        
        # base link 
        randomize_base_mass = (True and domain_rand_general)
        added_mass_range = [-3., 3]
        randomize_base_com = (True and domain_rand_general)
        added_com_range = [-0.06, 0.06]

        randomize_joint_friction = (True and domain_rand_general)
        joint_friction_range = [0.005, 0.02]
        # randomize_joint_damping = (True and domain_rand_general)
        # joint_damping_range = [0.1, 1.2]
        randomize_joint_armature = (True and domain_rand_general)
        joint_armature_range = [0.008, 0.02]  # target value

        randomize_pd_gains = (True and domain_rand_general)
        p_gain_range = [0.8, 1.2]
        d_gain_range = [0.8, 1.2]

        randomize_gravity = (True and domain_rand_general)
        gravity_rand_interval_s = 10
        gravity_range = (-0.1, 0.1)
    
    class noise(HumanoidCfg.noise):
        add_noise = True
        noise_level = 1.0
        noise_increasing_steps = 5000

        class noise_scales:
            dof_pos = 0.02
            dof_vel = 0.1
            lin_vel = 0.1
            ang_vel = 0.05
            gravity = 0.05
            imu = 0.05

class PMMiniMimicProprioCfgPPO(HumanoidCfgPPO):
    seed = 1

    class runner(HumanoidCfgPPO.runner):
        # student polcicy class
        policy_class_name = 'DualHistActorCritic'
        algorithm_class_name = 'PPO'
        runner_class_name = 'OnPolicyDagger'
        max_iterations = 15001  # number of policy updates
        
        # expert policy class
        expert_class_name = "ActorCritic"
        resume_expert = True
        load_expert_id = "mini_root_teacher_walk_dance_sets_100hz_scale0.25_p100d20_with_rand_tuned3"
        checkpoint_expert = 7000

        # logging
        save_interval = 100  # check for potential saves every this many iterations
        eval_interval = -1
        experiment_name = 'test'
        run_name = ''
        # load and resume
        resume = False
        load_run = -1  # -1 = last run
        checkpoint = -1  # -1 = last saved model
        resume_path = None  # updated from load_run and chkpt
    
    class expert_policy(HumanoidCfgPPO.policy):
        teacher_policy = True
        actor_hidden_dims = [1024, 512, 256, ]
        critic_hidden_dims = [1024, 512, 256, ]
        action_std = [0.3, 0.3, 0.3, 0.4, 0.2, 0.2, 0.1] * 2 + [0.1, 0.1] + [0.1] * 10

        # MCP-specific configs
        num_prims = 4
        ensemble_type = "mcp"
        composer_hidden_dims = [1024, 512, 256,]

    class policy(HumanoidCfgPPO.policy):
        teacher_policy = False
        actor_hidden_dims = [1024, 512, 256, ]
        critic_hidden_dims = [1024, 512, 256, ]
        fix_action_std = True
        action_std = [1.0, 1.0, 1.0, 1.0, 0.8, 0.8, 0.6] * 2 + [0.8, 0.8] + [0.6] * 10

    class algorithm(HumanoidCfgPPO.algorithm):
        expert_alpha = 0.1
        clip_param = 0.2
        entropy_coef = 0.005
        max_grad_norm = 1.0
        value_loss_coef = 1.0
        expert_gudiance_coef_schedual = [0.5, 0.1, 0, 2000]
        grad_penalty_coef_schedual = [0.001, 0.002, 1000, 2000]
        # grad_penalty_coef_schedual = None
    