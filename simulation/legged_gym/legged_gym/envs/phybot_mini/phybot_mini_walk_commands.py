from legged_gym.envs.phybot.phybot_mark2_mimic import PM2Mimic
import torch


class PMMiniWalkCommands(PM2Mimic):
    def __init__(self, cfg, sim_params, physics_engine, sim_device, headless):
        super().__init__(cfg, sim_params, physics_engine, sim_device, headless)

    def _get_body_indices(self):
        super()._get_body_indices()
        # exclude the fixed joints from the neck
        self.ref_dof_indices = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
                                12,
                                15, 16, 17, 18, 19, 20, 21, 22]
    
    def compute_observations(self):
        state_buf = self.compute_state_observation()
        # noise schedule when training
        if self.cfg.noise.add_noise and self.headless:
            state_buf += (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec * min(
                self.total_env_steps_counter / (self.cfg.noise.noise_increasing_steps * 24), 1.)
        elif self.cfg.noise.add_noise and not self.headless:
            state_buf += (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec
        else:
            state_buf += 0.

        if self.cfg.domain_rand.domain_rand_general and self.cfg.env.n_priv_latent > 0:
            priv_latent = torch.cat((
                self.mass_params_tensor,
                self.env_frictions,
                self.motor_strength[0] - 1,
                self.motor_strength[1] - 1,
            ), dim=-1)
        else:
            priv_latent = torch.zeros((self.num_envs, self.cfg.env.n_priv_latent), device=self.device)
        
        if self.cfg.env.n_priv > 0:
            feet_contact = self.contact_filt.float() - 0.5
            priv_explicit = torch.cat((
                self.root_states[:, 2:3],
                feet_contact,
                self.base_lin_vel * self.obs_scales.lin_vel,
                self.robot_local_kb_pos.view(self.num_envs, -1),
            ), dim=-1)
        else:
            priv_explicit = torch.zeros((self.num_envs, self.cfg.env.n_priv), device=self.device)

        obs_task = self.task_observation_buf.view(self.num_envs, -1)
        # mask the task relevant obs to zero
        obs_task *= 0

        self.obs_buf = torch.cat([state_buf, obs_task, priv_explicit, priv_latent, self.obs_history_buf.view(self.num_envs, -1)], dim=-1)

        if self.cfg.env.history_len > 0:
            self.obs_history_buf = torch.where(
                (self.episode_length_buf <= 1)[:, None, None],
                torch.stack([state_buf] * self.cfg.env.history_len, dim=1),
                torch.cat([
                    self.obs_history_buf[:, 1:],
                    state_buf.unsqueeze(1)
                ], dim=1)
            )
        self.contact_buf = torch.where(
            (self.episode_length_buf <= 1)[:, None, None],
            torch.stack([self.contact_filt.float()] * self.cfg.env.contact_buf_len, dim=1),  # replicative initialization.
            torch.cat([
                self.contact_buf[:, 1:],
                self.contact_filt.float().unsqueeze(1)
            ], dim=1)
        )
    
    def _reward_tracking_feet_contacts(self):
        cur_ref_feet_contacts = self.cur_ref_contact_profile.clone()
        ref_left_foot_contact_signal = cur_ref_feet_contacts[:, 0]
        ref_right_foot_contact_signal = cur_ref_feet_contacts[:, 1]  # considering the ref heel contact
        cur_ref_feet_contact_signal = torch.stack((ref_left_foot_contact_signal, ref_right_foot_contact_signal), dim=1)
        heel_contact = self.contact_forces[:, self.feet_indices, 2] > 5.
        r_feet_contact = torch.where(cur_ref_feet_contact_signal == heel_contact, 2.0, -1.0)
        # feet_vel = torch.norm(self.rigid_body_states[:, self.feet_indices, 7:10], dim=-1)
        # r_feet_vel = feet_vel * torch.where(cur_ref_feet_contact_signal == heel_contact, -1.0, 0.0)
        r = r_feet_contact
        return torch.mean(r, dim=-1)

    def _reward_tracking_ref_dof_pos(self):
        joint_pos = self.dof_pos.clone()
        ref_dof_pos = self.cur_ref_dof_pos.clone()
        diff = (joint_pos - ref_dof_pos)[:, :self.cfg.rewards.num_lower_joints]
        r = torch.exp(-1.0 * torch.norm(diff, dim=1)) - 0.2 * torch.norm(diff, dim=1).clamp(0, 0.5)
        return r
    
    def _reward_tracking_ref_dof_vel(self):
        joint_vel = self.dof_vel.clone()
        ref_dof_vel = self.cur_ref_dof_vel.clone()
        diff = (joint_vel - ref_dof_vel)[:, :self.cfg.rewards.num_lower_joints]
        r = torch.exp(-0.05 * torch.norm(diff, dim=1))
        return r
