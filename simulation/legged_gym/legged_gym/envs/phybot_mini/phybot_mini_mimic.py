from legged_gym.envs.phybot.phybot_mark2_mimic import PM2Mimic
import torch

class PMMiniMimic(PM2Mimic):
    def __init__(self, cfg, sim_params, physics_engine, sim_device, headless):
        super().__init__(cfg, sim_params, physics_engine, sim_device, headless)

    def _get_body_indices(self):
        super()._get_body_indices()
        # exclude the fixed joints from the neck
        self.ref_dof_indices = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
                                12,
                                15, 16, 17, 18, 19, 20, 21, 22]
    
    def _reward_tracking_feet_contacts(self):
        cur_ref_feet_contacts = self.cur_ref_contact_profile.clone()
        ref_left_foot_contact_signal = cur_ref_feet_contacts[:, 0]
        ref_right_foot_contact_signal = cur_ref_feet_contacts[:, 1]  # considering the ref heel contact
        cur_ref_feet_contact_signal = torch.stack((ref_left_foot_contact_signal, ref_right_foot_contact_signal), dim=1)
        heel_contact = self.contact_forces[:, self.feet_indices, 2] > 5.
        r = torch.where(cur_ref_feet_contact_signal == heel_contact, 2.0, -1.0)
        return torch.mean(r, dim=-1)
