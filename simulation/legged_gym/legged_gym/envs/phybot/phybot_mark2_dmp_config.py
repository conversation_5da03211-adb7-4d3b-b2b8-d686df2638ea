from legged_gym.envs.phybot.phybot_mark2_mimic_config import PM2MimicCfg
from legged_gym.envs.base.humanoid_config import HumanoidCfg, HumanoidCfgPPO


class PM2DMPCfg(PM2MimicCfg):
    class env(PM2MimicCfg.env):
        num_envs = 8192
        num_actions = num_dofs = 26
        n_proprio = 4 + 3 + 3 + 3 * num_actions
        n_priv_latent = 4 + 1 + 3 + 2 * num_dofs # 0
        history_len = 1

        n_key_bodies = 14
        num_future_steps = 1
        num_interval_steps = 4
        num_task_observations = 1 + 6 + 3 + 3 + 2 * num_dofs + 2 * n_key_bodies * 3
        num_planned_observations = num_task_observations # w/o contact, will add further.

        n_priv = 1 + 2 + 3 + 3 + n_key_bodies * 3

        num_observations = (history_len * n_proprio + n_proprio + n_priv_latent + n_priv
                            + num_planned_observations * num_future_steps)

        num_privileged_obs = None

        env_spacing = 3.  # not used with heightfields/trimeshes
        send_timeouts = True  # send time out information to the algorithm
        episode_length_s = 10

        randomize_start_pos = True
        randomize_start_yaw = True

        history_encoding = True
        contact_buf_len = 10

        normalize_obs = True
        relative_to_root = True

        enable_height_termination = True
        enable_orient_termination = True
        enable_max_kb_pos_err_cut = False

        termination_dist_curriculum = True
        init_termination_dist = 1.25
        dist_curriculum_scale = -0.5
    
    class normalization(PM2MimicCfg.normalization):
        clip_actions = 10

    class control(PM2MimicCfg.control):
        control_type = 'P'
        reference_type = "diffuser"
        action_scale = 0.25

    class sim(HumanoidCfg.sim):
        dt = 0.001

    class asset(PM2MimicCfg.asset):
        file = '{LEGGED_GYM_ROOT_DIR}/resources/robots/phybot/phybot_mark2.urdf'

    class motion_planner:
        dmp_cpkt = "{LEGGED_GYM_ROOT_DIR}/pretrained_models/pm2_camdm_kb_act_sep_limbs_cfg_cmu/best.pt"
        robot_mjcf_path = "{LEGGED_GYM_ROOT_DIR}/resources/robots/phybot/phybot_mark2_custom_collision.xml"
        inference_type = "closed_loop"
        guidance_scale = 0.7
        planning_dt = 0.03

        dof_obs_size = 14*6
        dof_group_offsets = [0, 3, 4, 6, 7, 10, 11, 13, 14, 16, 18, 21, 23, 26, 28]
        w_last = True
        dof_to_group_reindex = [1, 0, 2, 3, 5, 4, 6, 8, 7, 9, 10, 12, 11, 13,
                                15, 14, 17, 16,
                                19, 18, 20, 21, 22, 25, 24, 26, 27, 28]  # reindex to rpy order
        dof_group_types = [3, 1, 0, 1, 3, 1, 0, 1,
                           2, 4,
                           3, 4, 3, 4]  # hard encode the dof group types
        
        condition_keypoints = ["base_link", 
                               "left_knee", "left_ankle_roll", "left_toe",
                               "right_knee", "right_ankle_roll", "right_toe",
                               "neck_pitch",
                               "left_elbow_pitch", "left_wrist", 
                               "right_elbow_pitch", "right_wrist"]
        
        robot_dof_offset = [0, 3, 4, 6, 7, 10, 11, 13, 14, 16, 18, 21, 23, 24, 27, 29, 30]

        class arch:
            rot_req = "6d"
            decoder = "trans_enc"
            latent_dim = 256
            input_feats = 16*6
            njoints = 16
            nfeats = 6
            ff_size = 1024
            num_heads = 4
            num_layers = 4
            offset_frame = 1
            past_frame = 12
            future_frame = 12
            local_cond = "traj"
            global_cond = "style"
            dropout = 0.2
            activation = "gelu"
        class diff:
            noise_schedule = "cosine"
            diffusion_steps = 40
            sigma_small = True
        class trainer:
            cond_mask_prob= 0.15

    class motion_lib(PM2MimicCfg.motion_lib):
        motion_sync = False
        motion_dump = "walks_120fps_contact"


class PM2DMPCfgPPO(HumanoidCfgPPO):
    seed = 1

    class runner(HumanoidCfgPPO.runner):
        policy_class_name = "McpActorCritic"  # 'ActorCriticRMA'
        algorithm_class_name = 'PPO'  #  "PPORMA"
        runner_class_name = 'OnPolicyRunner'
        max_iterations = 5001  # number of policy updates

        # logging
        save_interval = 100  # check for potential saves every this many iterations
        eval_interval = 1000
        experiment_name = 'test'
        run_name = ''
        # load and resume
        resume = False
        load_run = -1  # -1 = last run
        checkpoint = -1  # -1 = last saved model
        resume_path = None  # updated from load_run and chkpt

    class policy(HumanoidCfgPPO.policy):
        teacher_policy = True
        actor_hidden_dims = [512, 256, 128, ]
        critic_hidden_dims = [1024, 512, 256, ]
        action_std = [0.3, 0.3, 0.3, 0.4, 0.2, 0.2, 0.1] * 2 + [0.1, 0.1] + [0.1] * 10

        # MCP-specific configs
        num_prims = 4
        ensemble_type = "mcp"
        composer_hidden_dims = [1024, 512, 256,]

    class algorithm(HumanoidCfgPPO.algorithm):
        grad_penalty_coef_schedual = [0.001, 0.002, 700, 1000]