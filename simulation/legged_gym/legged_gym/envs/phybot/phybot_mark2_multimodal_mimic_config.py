from legged_gym.envs.base.humanoid_config import HumanoidCfg, HumanoidCfgPPO
from legged_gym.envs.phybot.phybot_mark2_mimic_config import PM2MimicCfg


class PM2MultimodalMimicCfg(PM2MimicCfg):
    class env(PM2MimicCfg.env):
        num_envs = 8192
        num_actions = num_dofs = 26
        n_key_bodies = 14
        n_proprio = 4 + 3 + 3 + 3 * num_actions
        n_priv = 1 + 2 + 3 + n_key_bodies * 3
        n_priv_latent = 4 + 1 + 2 * num_actions
        
        num_future_steps = 3
        num_interval_steps = 1
        num_task_obs_inst = (1 + 6 + 3 + 3 + num_dofs + 2 * n_key_bodies * 3)
        num_task_observations = num_task_obs_inst * num_future_steps
        history_len = long_history_len = 20
        short_history_len = 5
        expert_history_len = 3

        num_observations = (n_proprio + num_task_observations + long_history_len * n_proprio)
        # for the critic and expert policy
        num_privileged_obs = (n_proprio + num_task_observations + n_priv + n_priv_latent + 
                            expert_history_len * n_proprio)

        env_spacing = 3.  # not used with heightfields/trimeshes
        send_timeouts = True  # send time out information to the algorithm
        episode_length_s = 10

        randomize_start_pos = True
        randomize_start_yaw = True

        history_encoding = True
        contact_buf_len = 10

        normalize_obs = True
        relative_to_root = True  # for expert only

        enable_height_termination = True
        enable_orient_termination = True
        enable_max_kb_pos_err_cut = True

        termination_dist_curriculum = True
        init_termination_dist = 1.25
        dist_curriculum_scale = -1.0

        height_offset = 0.02
    
    class rewards(PM2MimicCfg.rewards):
        joint_weights = [
            1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
            1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
            1.0, 1.0, 
            1.0, 1.0, 1.0, 1.0, 1.0,
            1.0, 1.0, 1.0, 1.0, 1.0,
        ]

        keybody_weights = [
            1.0, 1.0,  # base_keybody
            1.0, 1.0, 1.0, 1.0, 1.0, 1.0,  # lower_keybody
            1.0, 1.0, 1.0, 1.0, 1.0, 1.0,  # upper_keybody
        ]

        class scales:
            # task rew
            tracking_ref_dof_pos = 5.0
            tracking_ref_dof_vel = 1.0
            tracking_ref_kb_pos = 5.0
            # tracking_ref_height = 1.0
            # tracking_ref_yaw = 0.5
            # tracking_ref_roll_pitch = 0.5
            tracking_ref_kb_rot = 2.0
            tracking_ref_lin_vel = 3.0
            tracking_ref_ang_vel = 1.0
            tracking_ref_kb_lin_vel = 3.0
            tracking_ref_kb_ang_vel = 1.0
            tracking_feet_contacts = 2.0
            
            alive = 1.0

            # air-time walk style rew
            # single_support = 1.5
            # feet_air_time = 10.0
            # feet_height = 2.0
            # flying_phase = 1.0

            # limbs reg
            feet_stumble = -1.25 * 2
            feet_contact_forces = -1e-3
            foot_slip = -0.2
            collision = -10.0

            # control reg
            dof_pos_limits = -10
            action_rate = -0.2
            energy = -1e-3
            dof_torque_limits = -1.0
            dof_acc = -3e-7
            dof_vel = -1e-5
            delta_torques = -1.0e-7
            torque_penalty = -6e-7

    class motion_lib(PM2MimicCfg.motion_lib):
        motion_sync = False
        motion_dump = "cmu_walk_run500_120fps"
        pre_processed = False
        specified_ids = ["114_14_poses",]  # 02_04_poses, 91_62_poses
        num_to_load = -1
        sample_scheme = "dynamic" # preceding, dynamic, uniform

        global_trans_reset_time = 0.3
        bucket_width = 2
    
    class commands:
        curriculum = False
        resample = False
        num_commands = 3
        resampling_time = 3.  # time before command are changed[s]

        ang_vel_clip = 0.1
        lin_vel_clip = 0.1

        tracking_modes = ["Root", "WholeBody"]  # ["Root", "Telep", "WholeBody"]
        tracking_mode_probs = [0.2, 0.8]

        class ranges:
            lin_vel_x = [0., 1.6]  # min max [m/s]
            lin_vel_y = [-0.3, 0.3]
            ang_vel_yaw = [-0.6, 0.6]  # min max [rad/s]

    class sim(HumanoidCfg.sim):
        dt = 0.001

    class domain_rand:
        domain_rand_general = False  # manually set this, setting from parser does not work;

        # push_robots = (False and domain_rand_general)
        push_robots = True
        push_interval_s = 3
        max_push_vel_xy = 1.0
        max_push_ang_vel = 0.5

        randomize_motor = True
        motor_strength_range = [0.8, 1.2] # [0.8, 1.2]

        action_delay = False
        action_buf_len = 8

        # apply the dynamics randomization follow versatile_loco: https://arxiv.org/abs/2401.16889
        randomize_friction = (True and domain_rand_general)
        friction_range = [0.3, 1.5]

        # base link 
        randomize_base_mass = (True and domain_rand_general)
        added_mass_range = [-4., 4]
        randomize_base_com = (True and domain_rand_general)
        added_com_range = [-0.1, 0.1]

        randomize_joint_friction = (True and domain_rand_general)
        joint_friction_range = [0.01, 0.2]
        # randomize_joint_damping = (True and domain_rand_general)
        # joint_damping_range = [0.1, 1.2]
        randomize_joint_armature = (True and domain_rand_general)
        joint_armature_range = [0.008, 0.06]  # target value

        randomize_pd_gains = (True and domain_rand_general)
        p_gain_range = [0.8, 1.2]
        d_gain_range = [0.8, 1.2]

        randomize_gravity = (False and domain_rand_general)
        gravity_rand_interval_s = 10
        gravity_range = (-0.1, 0.1)

    class noise(HumanoidCfg.noise):
        add_noise = True
        noise_increasing_steps = 5000

        class noise_scales:
            dof_pos = 0.01
            dof_vel = 0.1
            lin_vel = 0.1
            ang_vel = 0.05
            gravity = 0.05
            imu = 0.05
    
    class asset(PM2MimicCfg.asset):
        upper_keybody_names = [
            "left_shoulder_roll", "left_elbow_pitch", "left_elbow_yaw",
            "right_shoulder_roll", "right_elbow_pitch", "right_elbow_yaw",
        ]
        lower_keybody_names = [
            "left_knee", "left_ankle_roll", "left_toe",
            "right_knee", "right_ankle_roll", "right_toe",
        ]
        base_keybody_names = ["base_link", "waist_roll",]


class PM2MultimodalMimicCfgPPO(HumanoidCfgPPO):
    seed = 1

    class runner(HumanoidCfgPPO.runner):
        # student polcicy class
        policy_class_name = 'DualHistActorCritic'
        algorithm_class_name = 'PPO'
        runner_class_name = 'OnPolicyDagger'
        max_iterations = 20001  # number of policy updates
        
        # expert policy class
        expert_class_name = "ActorCritic"
        resume_expert = True
        load_expert_id = "mimic_teacher_cmu_walk_run500_fut3int1_relative_root_all"
        checkpoint_expert = 4800

        # logging
        save_interval = 100  # check for potential saves every this many iterations
        eval_interval = -1
        experiment_name = 'test'
        run_name = ''
        # load and resume
        resume = False
        load_run = -1  # -1 = last run
        checkpoint = 4800  # -1 = last saved model
        resume_path = None  # updated from load_run and chkpt
    
    class expert_policy(HumanoidCfgPPO.policy):
        teacher_policy = True
        actor_hidden_dims = [1024, 512, 256, ]
        critic_hidden_dims = [1024, 512, 256, ]
        action_std = [0.3, 0.3, 0.3, 0.4, 0.2, 0.2, 0.1] * 2 + [0.1, 0.1] + [0.1] * 10

        # MCP-specific configs
        num_prims = 4
        ensemble_type = "mcp"
        composer_hidden_dims = [1024, 512, 256,]

    class policy(HumanoidCfgPPO.policy):
        teacher_policy = False
        actor_hidden_dims = [1024, 512, 256, ]
        critic_hidden_dims = [1024, 512, 256, ]
        fix_action_std = True
        action_std = [1.0, 1.0, 1.0, 1.0, 0.8, 0.8, 0.6] * 2 + [0.8, 0.8] + [0.6] * 10

    class algorithm(HumanoidCfgPPO.algorithm):
        expert_alpha = 0.1
        clip_param = 0.15
        entropy_coef = 0.005
        max_grad_norm = 1.0
        value_loss_coef = 1.0
        expert_gudiance_coef_schedual = [1.0, 0.1, 0, 5000]
        # grad_penalty_coef_schedual = [0.001, 0.002, 2000, 10000]
        grad_penalty_coef_schedual = None
