from legged_gym import LEGGED_GYM_ROOT_DIR, envs
from time import time
from warnings import WarningMessage
import numpy as np
from isaacgym.torch_utils import *
from isaacgym import gymtorch, gymapi, gymutil
import torch, torchvision
from legged_gym import LEGGED_GYM_ROOT_DIR, POSE_DIR
from legged_gym.envs.base.base_task import BaseTask
from .phybot_mark2_mimic_proprio_config import PM2MimicProprioCfg
from .phybot_mark2_mimic import PM2Mimic, transform_global_to_local, transform_local_to_global
from legged_gym.envs.base.humanoid import Humanoid
from legged_gym.envs.base.legged_robot import euler_from_quaternion
from legged_gym.gym_utils.math import *
from legged_gym.utils.motion_lib_base import MotionLib
from legged_gym.utils.torch_utils import my_quat_rotate, calc_heading_quat_inv, calc_heading_quat


class PM2MimicProprio(PM2Mimic):
    def __init__(self, cfg, sim_params, physics_engine, sim_device, headless):
        super().__init__(cfg, sim_params, physics_engine, sim_device, headless)
        # create the task obs buffer for student
        self.student_task_obs_buf = torch.zeros((self.num_envs, self.cfg.env.num_task_observations),
                                                 device=self.device, dtype=torch.float32)
        self.student_root_commands = torch.zeros((self.num_envs, 4), device=self.device)
        # the noiseless state history buf for the expert
        self.expert_history_buf = torch.zeros(self.num_envs, self.cfg.env.expert_history_len, self.cfg.env.n_proprio, 
                                           device=self.device, dtype=torch.float)

    def compute_observations(self):
        state_buf = self.compute_state_observation()
        # noise schedule when training
        if self.cfg.noise.add_noise and self.headless:
            noise_state_buf = state_buf + (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec * min(
                self.total_env_steps_counter / (self.cfg.noise.noise_increasing_steps * 24), 1.)
        elif self.cfg.noise.add_noise and not self.headless:
            noise_state_buf = state_buf + (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec
        else:
            noise_state_buf = state_buf.clone()
        noise_state_buf[:, 0:4] = self.student_root_commands
        # the privileged observation for the expert policy
        # expert policy that shares the same domian rand setting
        if self.cfg.domain_rand.domain_rand_general and self.cfg.env.n_priv_latent > 0:
            priv_rand = torch.cat((
                self.mass_params_tensor,
                self.env_frictions,
                self.motor_strength[0] - 1,
                self.motor_strength[1] - 1,
            ), dim=-1)
        else:
            priv_rand = torch.zeros((self.num_envs, self.cfg.env.n_priv_latent), device=self.device)
        # the explict priv state used by the expert policy
        if self.cfg.env.n_priv > 0:
            feet_contact = self.contact_filt.float() - 0.5
            priv_explicit = torch.cat((
                self.root_states[:, 2:3],
                feet_contact,
                self.base_lin_vel * self.obs_scales.lin_vel,
                self.robot_local_kb_pos.view(self.num_envs, -1),
            ), dim=-1)
        else:
            priv_explicit = torch.zeros((self.num_envs, self.cfg.env.n_priv), device=self.device)
        
        student_task_obs = self.student_task_obs_buf.view(self.num_envs, -1)
        self.obs_buf = torch.cat([noise_state_buf, student_task_obs, self.obs_history_buf.view(self.num_envs, -1)], dim=-1)
        
        expert_task_obs = self.task_observation_buf.view(self.num_envs, -1)
        self.privileged_obs_buf = torch.cat([state_buf, expert_task_obs, priv_explicit, priv_rand,
                                              self.expert_history_buf.view(self.num_envs, -1)], dim=-1)

        # update the history bufs
        if self.cfg.env.history_len > 0:
            self.obs_history_buf = torch.where(
                (self.episode_length_buf <= 1)[:, None, None],
                torch.stack([noise_state_buf] * self.cfg.env.history_len, dim=1),
                torch.cat([
                    self.obs_history_buf[:, 1:],
                    noise_state_buf.unsqueeze(1)
                ], dim=1)
            )
        if self.cfg.env.expert_history_len > 0:
            self.expert_history_buf = torch.where(
                (self.episode_length_buf <= 1)[:, None, None],
                torch.stack([state_buf] * self.cfg.env.expert_history_len, dim=1),
                torch.cat([
                    self.expert_history_buf[:, 1:],
                    state_buf.unsqueeze(1)
                ], dim=1)
            )

        self.contact_buf = torch.where(
            (self.episode_length_buf <= 1)[:, None, None],
            torch.stack([self.contact_filt.float()] * self.cfg.env.contact_buf_len, dim=1),  # replicative initialization.
            torch.cat([
                self.contact_buf[:, 1:],
                self.contact_filt.float().unsqueeze(1)
            ], dim=1)
        )
    
    # # calculate two version of task obs
    # def update_student_ref_motion_obs(self):
    #     ## ref motion at future env steps for task observation
    #     next_ref_motion = self.motion_lib.get_motion_state(self.next_ref_motion_ids.view(-1), self.next_ref_motion_times.view(-1))
    #     # extract the cmd/task obs from the ref motion
    #     next_ref_root_rot = next_ref_motion.rb_rot[:, 0].clone()
    #     next_ref_root_vel = next_ref_motion.rb_vel[:, 0].clone()
    #     next_ref_root_ang_vel = next_ref_motion.rb_ang_vel[:, 0].clone()
    #     next_ref_kb_pos = next_ref_motion.rb_pos[:, self.ref_kb_indices].clone()
    #     next_ref_kb_vel = next_ref_motion.rb_vel[:, self.ref_kb_indices].clone()
    #     # extracted root command
    #     next_ref_root_height = next_ref_motion.rb_pos[:, 0, 2:3].clone()  # (n_env*n_steps, 1)
    #     next_root_proj_grav = quat_rotate_inverse(next_ref_root_rot, self.gravity_vec.repeat(self.num_future_steps, 1)) # (n_env*n_steps, 3)
    #     next_root_facing_vec = quat_rotate(next_ref_root_rot, self.forward_vec.repeat(self.num_future_steps, 1))
    #     # extract dof command
    #     next_ref_dof_pos = next_ref_motion.dof_pos[:, self.ref_dof_indices].clone()

    #     # extract the basic ref states for each next target states
    #     rolled_ref_root_rot = next_ref_root_rot.reshape(self.num_envs, self.num_future_steps, 4).roll(shifts=1, dims=1)
    #     rolled_ref_root_vel = next_ref_root_vel.reshape(self.num_envs, self.num_future_steps, 3).roll(shifts=1, dims=1)
    #     rolled_ref_root_ang_vel = next_ref_root_ang_vel.reshape(self.num_envs, self.num_future_steps, 3).roll(shifts=1, dims=1)
    #     rolled_ref_kb_pos = next_ref_kb_pos.reshape(self.num_envs, self.num_future_steps, len(self.ref_kb_indices), 3).roll(shifts=1, dims=1)
    #     rolled_ref_kb_vel = next_ref_kb_vel.reshape(self.num_envs, self.num_future_steps, len(self.ref_kb_indices), 3).roll(shifts=1, dims=1)
    #     rolled_ref_dof_pos = next_ref_dof_pos.reshape(self.num_envs, self.num_future_steps, -1).roll(shifts=1, dims=1)

    #     ## derive the absolute ref state w.r.t to the cur priv root state for the privileged_obs
    #     rolled_ref_root_rot[:, 0] = self.cur_ref_root_rot.clone()
    #     rolled_ref_root_vel[:, 0] = self.cur_ref_root_vel.clone()
    #     rolled_ref_root_ang_vel[:, 0] = self.cur_ref_root_ang_vel.clone()
    #     rolled_ref_kb_pos[:, 0] = transform_local_to_global(self.cur_local_kb_pos.clone(), self.cur_ref_root_pos.clone(),
    #                                                              self.cur_ref_root_rot.clone())
    #     rolled_ref_kb_vel[:, 0] = self.cur_ref_kb_vel.clone()
    #     rolled_ref_dof_pos[:, 0] = self.cur_ref_dof_pos.clone()

    #     rolled_ref_root_rot_flat = rolled_ref_root_rot.reshape(-1, 4)
    #     rolled_ref_root_vel_flat = rolled_ref_root_vel.reshape(-1, 3)
    #     rolled_ref_root_ang_vel_flat = rolled_ref_root_ang_vel.reshape(-1, 3)
    #     rolled_ref_kb_pos_flat = rolled_ref_kb_pos.reshape(-1, len(self.key_body_indices), 3)
    #     rolled_ref_kb_vel_flat = rolled_ref_kb_vel.reshape(-1, len(self.key_body_indices), 3)
    #     rolled_ref_dof_pos_flat = rolled_ref_dof_pos.reshape(-1, self.num_dof)

    #     rolled_root_facing_vec = quat_rotate(rolled_ref_root_rot_flat, self.forward_vec.repeat(self.num_future_steps, 1))
    #     target_delta_facing_vec = (next_root_facing_vec - rolled_root_facing_vec).reshape(self.num_envs, self.num_future_steps, 3)
        
    #     rolled_root_proj_grav = quat_rotate_inverse(rolled_ref_root_rot_flat, self.gravity_vec.repeat(self.num_future_steps, 1))
    #     target_delta_proj_grav = (next_root_proj_grav - rolled_root_proj_grav).reshape(self.num_envs, self.num_future_steps, 3)

    #     target_local_root_vel = quat_rotate_inverse(rolled_ref_root_rot_flat, 
    #                                                     next_ref_root_vel - rolled_ref_root_vel_flat).reshape(self.num_envs, self.num_future_steps, 3)
    #     target_local_root_ang_vel = quat_rotate_inverse(rolled_ref_root_rot_flat,
    #                                                         next_ref_root_ang_vel - rolled_ref_root_ang_vel_flat).reshape(self.num_envs, self.num_future_steps, 3)

    #     rolled_ref_root_rot_expand = rolled_ref_root_rot_flat.unsqueeze(-2).repeat(1, len(self.key_body_indices), 1)
    #     next_ref_kb_pos_flat = next_ref_kb_pos.reshape(-1, len(self.ref_kb_indices), 3)
    #     target_local_kb_pos = quat_rotate_inverse(rolled_ref_root_rot_expand.view(-1, 4), 
    #                                                 (next_ref_kb_pos_flat - rolled_ref_kb_pos_flat).view(-1, 3)).reshape(self.num_envs, self.num_future_steps, -1)
    #     # mask the trainsition frame with large global postion change.
    #     max_target_local_kb_diff = target_local_kb_pos.abs().max(-1, keepdim=False)[0]
    #     large_diff_mask = (max_target_local_kb_diff > 0.1).unsqueeze(-1).expand_as(target_local_kb_pos)
    #     target_local_kb_pos = torch.where(large_diff_mask, torch.zeros_like(target_local_kb_pos), target_local_kb_pos)

    #     next_ref_kb_vel_flat = next_ref_kb_vel.reshape(-1, len(self.ref_kb_indices), 3)
    #     target_local_kb_vel = quat_rotate_inverse(rolled_ref_root_rot_expand.view(-1, 4),
    #                                             (next_ref_kb_vel_flat - rolled_ref_kb_vel_flat).view(-1, 3)).reshape(self.num_envs, self.num_future_steps, -1)

    #     target_dof_pos = (next_ref_dof_pos - rolled_ref_dof_pos_flat).reshape(self.num_envs, self.num_future_steps, self.num_dof)

    #     student_target_task_obs = torch.cat([
    #         next_ref_root_height.view(self.num_envs, self.num_future_steps, 1),
    #         target_delta_proj_grav,
    #         target_delta_facing_vec,
    #         target_local_root_vel * self.obs_scales.lin_vel,
    #         target_local_root_ang_vel * self.obs_scales.ang_vel,
    #         target_dof_pos * self.obs_scales.dof_pos,
    #         target_local_kb_pos,
    #         target_local_kb_vel,
    #     ], dim=-1).view(self.num_envs, -1)
    #  self.student_task_obs_buf[:] = student_target_task_obs

    def update_student_ref_motion_obs(self):
        ## ref motion at future env steps for task observation
        next_ref_motion = self.motion_lib.get_motion_state(self.next_ref_motion_ids.view(-1), self.next_ref_motion_times.view(-1))
        # extract the cmd/task obs from the ref motion
        next_ref_root_pos = next_ref_motion.rb_pos[:, 0].clone()
        next_ref_root_rot = next_ref_motion.rb_rot[:, 0].clone()
        next_ref_root_vel = next_ref_motion.rb_vel[:, 0].clone()
        next_ref_root_ang_vel = next_ref_motion.rb_ang_vel[:, 0].clone()
        next_ref_kb_pos = next_ref_motion.rb_pos[:, self.ref_kb_indices].clone()
        next_ref_kb_vel = next_ref_motion.rb_vel[:, self.ref_kb_indices].clone()
        # extract dof command
        next_ref_dof_pos = next_ref_motion.dof_pos[:, self.ref_dof_indices].clone()
        # extracted root command
        next_ref_root_height = next_ref_motion.rb_pos[:, 0, 2:3].reshape(self.num_envs, self.num_future_steps, 1)
        next_root_proj_grav = (quat_rotate_inverse(next_ref_root_rot, self.gravity_vec.repeat(self.num_future_steps, 1))
                               .reshape(self.num_envs, self.num_future_steps, 3)) # (n_env, n_steps, 3)

        next_root_facing_vec = (quat_rotate(next_ref_root_rot, self.forward_vec.repeat(self.num_future_steps, 1))
                                .reshape(self.num_envs, self.num_future_steps, 3))

        CentricRelative = False
        if CentricRelative:
            centric_root_proj_grav = quat_rotate_inverse(self.cur_ref_root_rot.clone(),
                                                          self.gravity_vec)[:, None].repeat(1, self.num_future_steps, 1)
            next_root_proj_grav -= centric_root_proj_grav
            centric_root_facing_vec = quat_rotate(self.cur_ref_root_rot.clone(), 
                                                  self.forward_vec)[:, None].repeat(1, self.num_future_steps, 1)
            next_root_facing_vec -= centric_root_facing_vec
            centric_ref_root_rot = self.cur_ref_root_rot.clone()[:, None].repeat(1, self.num_future_steps, 1).reshape(-1, 4)
            next_ref_root_rot = centric_ref_root_rot
        
        next_local_root_vel = quat_rotate_inverse(next_ref_root_rot, next_ref_root_vel).reshape(self.num_envs, self.num_future_steps, 3) 
        next_local_root_ang_vel = quat_rotate_inverse(next_ref_root_rot, next_ref_root_ang_vel).reshape(self.num_envs, self.num_future_steps, 3)

        # extract dof command
        next_dof_pos_diff = ((next_ref_dof_pos - self.dof_pos.clone().repeat(self.num_future_steps, 1))
                             .reshape(self.num_envs, self.num_future_steps, self.num_dof))
        
        # extract local keypoint pos command
        next_local_kb_pos = (transform_global_to_local(next_ref_kb_pos, next_ref_root_pos, next_ref_root_rot)
                             .reshape(self.num_envs, self.num_future_steps, -1))  # (n_env*n_steps, n_kb, 3)
        next_local_kb_vel = (transform_global_to_local(next_ref_kb_vel, next_ref_root_vel, next_ref_root_rot)
                             .reshape(self.num_envs, self.num_future_steps, -1))

        # n_steps*(26+3+3+3+1+n_kb*3)
        self.student_task_obs_buf[:] = torch.cat([next_ref_root_height,
                                                  next_root_proj_grav,
                                                  next_root_facing_vec,
                                                  next_local_root_vel * self.obs_scales.lin_vel,
                                                  next_local_root_ang_vel * self.obs_scales.ang_vel,
                                                  next_dof_pos_diff * self.obs_scales.dof_pos,
                                                  next_local_kb_pos,
                                                  next_local_kb_vel,
                                                  ], dim=-1).view(self.num_envs, -1)
    
        self.student_root_commands[:, 0:2] = next_local_root_vel[:, 0, 0:2]
        self.student_root_commands[:, 0] *= torch.abs(self.root_commands[..., 0]) > self.cfg.commands.lin_vel_clip
        self.student_root_commands[:, 1] *= torch.abs(self.root_commands[..., 1]) > self.cfg.commands.lin_vel_clip
        self.student_root_commands[:, 2] = next_local_root_ang_vel[:, 0, 2] * self.obs_scales.ang_vel
        self.student_root_commands[:, 2] *= torch.abs(self.root_commands[..., 2]) > self.cfg.commands.ang_vel_clip
        self.student_root_commands[:, 3] = next_ref_root_height[:, 0, 0]

    def post_physics_step(self):
        Humanoid.post_physics_step(self)
        # step the motion lib
        self.motion_times += self.dt
        self.motion_times = torch.fmod(self.motion_times, self.motion_lib.get_motion_tick_spans(self.motion_ids))
        self._update_goals()
        env_ids = np.arange(self.num_envs)
        self.next_ref_motion_indices(env_ids, self.num_future_steps, self.num_interval_steps)
        self.update_student_ref_motion_obs()
        self.update_ref_motion_obs()

        if self.cfg.motion_lib.sample_scheme == "dynamic":
            if self.global_counter % self.weight_refresh_interval == 0:
                self.refresh_dynamic_weights()

        if self.viewer and self.enable_viewer_sync and self.debug_viz:
            if self.cfg.motion_lib.motion_sync:
                self._motion_sync()
                self.gym.refresh_actor_root_state_tensor(self.sim)
                self.gym.refresh_net_contact_force_tensor(self.sim)
                self.gym.refresh_rigid_body_state_tensor(self.sim)
                self.gym.refresh_force_sensor_tensor(self.sim)
            self.gym.clear_lines(self.viewer)
            self.draw_key_bodies_demo()
            
        return