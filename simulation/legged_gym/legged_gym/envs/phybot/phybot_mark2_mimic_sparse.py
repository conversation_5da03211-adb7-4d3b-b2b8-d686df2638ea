import torch
import numpy as np
from .phybot_mark2_mimic import PM2Mimic, transform_local_to_global
from .phybot_mark2_mimic_sparse_config import PM2MimicSparseCfg
from legged_gym.gym_utils.helpers import class_to_dict
from legged_gym.envs.base.humanoid import Humanoid
from enum import Enum


class PM2MimicSparse(PM2Mimic):
    def __init__(self, cfg: PM2MimicSparseCfg, sim_params, physics_engine, sim_device, headless):
        self.cfg = cfg
        self.global_trans_reset_time = cfg.motion_lib.global_trans_reset_time
        self.termination_dist_curriculum = cfg.env.termination_dist_curriculum
        self.use_estimator = (self.cfg.env.n_priv != 0)
        # read the new reward groups 
        self.constraints_scales = class_to_dict(self.cfg.constraints.scales)
        Humanoid.__init__(self, cfg, sim_params, physics_engine, sim_device, headless)
        self.last_feet_z = 0.05
        self.episode_length = torch.zeros((self.num_envs), device=self.device)
        self.feet_height = torch.zeros((self.num_envs, 2), device=self.device)
        self.state_init = self.StateInit[cfg.init_state.mode]

        # assign step to reach counter for the very first next_keyframe
        self.steps_to_next_keyframe = torch.zeros((self.num_envs, ), dtype=torch.long, device=self.device)
        self.steps_to_keyframe_range = self.cfg.motion_lib.steps_to_keyframe_range
        
        # override the default rew buf using a grouped one
        self.rew_buf = torch.zeros((self.num_envs, self.cfg.rewards.num_reward_groups), device=self.device, dtype=torch.float)
        self.reward_groups = self.cfg.rewards.reward_groups
        self.reward_group_weights = self.cfg.rewards.reward_group_weights

        self.shoulder_body_indices = torch.zeros(2, dtype=torch.long, device=self.device, requires_grad=False)
        tracking_shoulder_names = ["left_shoulder_roll", "right_shoulder_roll"]
        for k in range(2):
            self.shoulder_body_indices[k] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0], 
                                                                             tracking_shoulder_names[k])
        # we need to index from ref_kb buffer
        self.ref_shoulder_indices = [self.cfg.asset.key_body_names.index(name) for name in tracking_shoulder_names]

        lower_body_names = ["left_knee", "right_knee", "left_ankle_roll", "right_ankle_roll"]
        self.robot_lower_indices = torch.zeros(len(lower_body_names), dtype=torch.long, device=self.device, requires_grad=False)
        for k in range(len(lower_body_names)):
            self.robot_lower_indices[k] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0], 
                                                                             lower_body_names[k])
        self.ref_lower_indices = [self.cfg.asset.key_body_names.index(name) for name in lower_body_names]

        self._load_motion_lib(cfg.motion_lib)
        self._init_motion_buffer(cfg.motion_lib)
        self.reset_idx(torch.tensor(range(self.num_envs), device=self.device), init=True)
    
    def next_ref_motion_indices(self, env_ids, num_future_steps=1, num_interval_steps=1):
        # we only update the next keyframes when the step to reach it becomes 0.
        steps_to_keyframe = self.steps_to_next_keyframe
        update_mask = torch.nonzero(steps_to_keyframe == 0, as_tuple=False).flatten()
        if len(update_mask) > 0:
            # first resample the step to reach the next keyframe
            steps_to_keyframe_resample = torch.randint(self.steps_to_keyframe_range[0], self.steps_to_keyframe_range[1], 
                                                      (len(update_mask), ), device=self.device)
            self.steps_to_next_keyframe[update_mask] = steps_to_keyframe_resample

            time_offsets = self.dt * (steps_to_keyframe_resample[:, None] 
                                      + torch.arange(num_future_steps, device=self.device)[None, :] * num_interval_steps)
            future_times = time_offsets + self.motion_times[update_mask].unsqueeze(-1)  # (num_update_envs, fut_motion_steps)
            motion_ids = self.motion_ids[update_mask].unsqueeze(-1).repeat(1, num_future_steps)
            motion_time_spans = self.motion_lib.get_motion_tick_spans(motion_ids.reshape(-1))
            flat_times = torch.fmod(future_times.reshape(-1), motion_time_spans)
            reshaped_times = flat_times.reshape(update_mask.shape[0], num_future_steps)

            self.next_ref_motion_ids[update_mask] = motion_ids
            self.next_ref_motion_times[update_mask] = reshaped_times
        # update the step_to_reach counter
        self.steps_to_next_keyframe -= 1
        self.steps_to_next_keyframe[self.steps_to_next_keyframe < 0] = 0
    
    def _prepare_reward_function(self):
        # remove zero scales + multiply non-zero ones by dt
        for key in list(self.reward_scales.keys()):
            scale = self.reward_scales[key]
            if scale==0:
                self.reward_scales.pop(key)
            else:
                # ensure the reward scale to be 1 when using multiplicative form
                self.reward_scales[key] *= self.dt
        # prepare list of functions
        self.reward_functions = []
        self.reward_names = []
        for name, scale in self.reward_scales.items():
            if name=="termination":
                continue
            self.reward_names.append(name)
            name = '_reward_' + name
            self.reward_functions.append(getattr(self, name))
        # reward episode sums
        self.episode_sums = {name: torch.zeros(self.num_envs, dtype=torch.float, device=self.device, requires_grad=False)
                             for name in self.reward_scales.keys()}

        for key in list(self.constraints_scales.keys()):
            scale = self.constraints_scales[key]
            if scale==0:
                self.constraints_scales.pop(key) 
            else:
                # assign a coef for the dense reward part
                self.constraints_scales[key] *= self.dt
        self.constraint_functions = []
        self.constraint_names = []
        for name, scale in self.constraints_scales.items():
            self.constraint_names.append(name)
            name = '_reward_' + "_".join(name.split("_")[1:])
            self.constraint_functions.append(getattr(self, name))
        # constraint episode sums
        self.episode_sums.update({name: torch.zeros(self.num_envs, dtype=torch.float, device=self.device, requires_grad=False)
                             for name in self.constraints_scales.keys()})
        # combine the scales for logging
        self.reward_scales.update(self.constraints_scales)

    def compute_reward(self):
        self.rew_buf[:, :] = 0
        # compute the sparse keyframe task reward once reaching the keyframe
        steps_to_keyframe = self.steps_to_next_keyframe
        rewarding_mask = torch.nonzero(steps_to_keyframe == 0, as_tuple=False).flatten()
        task_group_index = self.reward_groups.index("task")
        # self.rew_buf[rewarding_mask, task_group_index] = 1 # shaped as multiplicative form.
        for i in range(len(self.reward_functions)):
            name = self.reward_names[i]
            rew = self.reward_functions[i]() * self.reward_scales[name]
            self.rew_buf[rewarding_mask, task_group_index] += rew[rewarding_mask]
            self.episode_sums[name] += rew
        # self.rew_buf[rewarding_mask, task_group_index] *= (np.mean(self.steps_to_keyframe_range))

        # compute the dense regularization and style rewards
        for j in range(len(self.constraint_names)):
            name = self.constraint_names[j]
            reward_group_name = name.split("_")[0]
            rew = self.constraint_functions[j]() * self.constraints_scales[name]
            reward_group_index = self.reward_groups.index(reward_group_name)
            self.rew_buf[:, reward_group_index] += rew
            self.episode_sums[name] += rew
            if self.cfg.rewards.only_positive_rewards:
                self.rew_buf[:, reward_group_index] = torch.clip(self.rew_buf[:, reward_group_index], min=0.)

        if "termination" in self.constraints_scales:
            rew = self._reward_termination() * self.constraints_scales["termination"]
            self.rew_buf += rew
            self.episode_sums["termination"] += rew
        
        if self.cfg.constraints.alive > 0:
            self.rew_buf += self._reward_alive() * self.cfg.constraints.alive * self.dt


    # ======================================================================================================================
    # Reward functions
    # ======================================================================================================================
    def _reward_tracking_ref_shoulder_pos(self):
        cur_local_shoulder_pos = self.cur_local_kb_pos.clone()[:, self.ref_shoulder_indices]
        target_global_root_pos = torch.cat([self.target_global_root_trans, self.cur_ref_root_height], dim=-1)
        global_root_rot = self.cur_ref_root_rot[:].clone()
        cur_ref_shoulder_pos = transform_local_to_global(cur_local_shoulder_pos, target_global_root_pos, global_root_rot).view(self.num_envs, -1)
        cur_robot_shoulder_pos = self.rigid_body_states[:, self.shoulder_body_indices, :3].view(self.num_envs, -1)
        diff = cur_ref_shoulder_pos - cur_robot_shoulder_pos
        r = torch.exp(-1.0 * torch.norm(diff, dim=1))
        return r

    def _reward_tracking_ref_lower_pos(self):
        cur_local_lower_pos = self.cur_local_kb_pos.clone()[:, self.ref_lower_indices]
        target_global_root_pos = torch.cat([self.target_global_root_trans, self.cur_ref_root_height], dim=-1)
        global_root_rot = self.cur_ref_root_rot[:].clone()
        cur_ref_lower_pos = transform_local_to_global(cur_local_lower_pos, target_global_root_pos, global_root_rot).view(self.num_envs, -1)
        cur_robot_lower_pos = self.rigid_body_states[:, self.robot_lower_indices, :3].view(self.num_envs, -1)
        diff = cur_ref_lower_pos - cur_robot_lower_pos
        r = torch.exp(-1.0 * torch.norm(diff, dim=1))
        return r
