from legged_gym import LEGGED_GYM_ROOT_DIR, envs
from time import time
from warnings import WarningMessage
import numpy as np
from isaacgym.torch_utils import *
from isaacgym import gymtorch, gymapi, gymutil
import torch, torchvision
from legged_gym import LEGGED_GYM_ROOT_DIR, POSE_DIR
from legged_gym.envs.base.base_task import BaseTask
from legged_gym.envs.base.humanoid import Humanoid
from legged_gym.utils.torch_humanoid_batch import HumanoidBatch
from .phybot_mark2_mimic import PM2Mimic
from legged_gym.envs.base.legged_robot import euler_from_quaternion
from legged_gym.gym_utils.math import *
from legged_gym.utils import torch_utils
from legged_gym.utils.torch_utils import dof_to_obs
from legged_gym.utils.rotation_conversions import matrix_to_quaternion, wxyz_to_xyzw
from rsl_rl.modules.motion_diffusion import MotionDiffusion
from rsl_rl.diffusion.create_diffusion import create_gaussian_diffusion
from enum import Enum
from collections import deque
from typing import List, Union, Tuple, Dict
from scipy.spatial.transform import Rotation as sRot
from rsl_rl.diffusion.nn_transforms import get_rotation, repr6d2quat
from easydict import EasyDict


class PM2DMP(PM2Mimic):
    def __init__(self, cfg, sim_params, physics_engine, sim_device, headless):
        self.cfg = cfg
        self.global_trans_reset_time = cfg.motion_lib.global_trans_reset_time
        self.termination_dist_curriculum = cfg.env.termination_dist_curriculum
        self.use_estimator = (self.cfg.env.n_priv != 0)
        self.global_trans_reset_time = cfg.motion_lib.global_trans_reset_time
        Humanoid.__init__(self, cfg, sim_params, physics_engine, sim_device, headless)
        self.last_feet_z = 0.05
        self.episode_length = torch.zeros((self.num_envs), device=self.device)
        self.feet_height = torch.zeros((self.num_envs, 2), device=self.device)
        self.state_init = self.StateInit[cfg.init_state.mode]
        self._load_motion_lib(cfg.motion_lib)
        self._init_motion_buffer(cfg.motion_lib)

        self.mp_cfg = cfg.motion_planner
        self.inference_type = cfg.motion_planner.inference_type
        self.guidance_scale = cfg.motion_planner.guidance_scale
        robot_mjcf_path = self.mp_cfg.robot_mjcf_path.format(LEGGED_GYM_ROOT_DIR=LEGGED_GYM_ROOT_DIR)
        self.robot_fk = HumanoidBatch(robot_mjcf_path)
        
        self.dmp_model = MotionDiffusion(self.mp_cfg)
        model_ckpt = self.mp_cfg.dmp_cpkt.format(LEGGED_GYM_ROOT_DIR=LEGGED_GYM_ROOT_DIR)
        if model_ckpt is not None:
            checkpoint = torch.load(model_ckpt)
            self.dmp_model.load_state_dict(checkpoint["state_dict"])
            self.dmp_model.to(self.device)
            self.dmp_model.eval()
            self.dmp_model.training = False
            print(f"Have successfully loaded the motion planner model from {model_ckpt} !!!")
        self.mp_diffuser = create_gaussian_diffusion(self.mp_cfg)

        self.num_dof_groups = 15  # root and 14 dof groups
        self.past_frame = self.mp_cfg.arch.past_frame
        self.fut_frame = self.mp_cfg.arch.future_frame
        self.planning_dt = self.mp_cfg.planning_dt
        self.planning_interval_step = round(self.planning_dt / self.dt)
        self.planned_motion_span = torch.tensor(0.6 * self.planning_dt * self.fut_frame, dtype=torch.float32, device=self.device)
        self.robot_dof_offset = self.mp_cfg.robot_dof_offset
        
        ref_rigid_body_names = self.cfg.motion_lib.rigid_body_names
        self.condition_kb_indices = torch.tensor([ref_rigid_body_names.index(name) for name in self.mp_cfg.condition_keypoints],
                                                 dtype=torch.long, device=self.device)
        # pre-allocate the motion planner buffer
        self._init_dmp_buffer()

        self.reset_idx(torch.tensor(range(self.num_envs), device=self.device), init=True)

    def _init_dmp_buffer(self):
        # init the current planned ref state for motion tracking
        self.cur_planned_dof_pos = torch.zeros_like(self.cur_ref_dof_pos)
        self.cur_planned_dof_vel = torch.zeros_like(self.cur_ref_dof_vel)
        self.cur_planned_delta_root_pos = torch.zeros_like(self.cur_ref_root_pos)
        self.cur_planned_root_height = torch.zeros_like(self.cur_ref_root_height)
        self.cur_planned_root_rot = torch.zeros_like(self.cur_ref_root_rot)
        self.cur_planned_root_vel = torch.zeros_like(self.cur_ref_root_vel)
        self.cur_planned_root_ang_vel = torch.zeros_like(self.cur_ref_root_ang_vel)
        self.cur_planned_local_kb_pos = torch.zeros_like(self.cur_local_kb_pos)
        self.cur_planned_kb_vel = torch.zeros_like(self.cur_ref_kb_vel)
        self.cur_planned_kb_ang_vel = torch.zeros_like(self.cur_ref_kb_ang_vel)

        # init the task obs from the planned motion
        self.planned_observation_buf = torch.zeros((self.num_envs, self.num_future_steps, 
                                                    self.cfg.env.num_planned_observations), device=self.device, dtype=torch.float32)
        self.planned_commands = torch.zeros((self.num_envs, 4), device=self.device, dtype=torch.float32)
        _, _, self.planned_yaw = euler_from_quaternion(self.cur_planned_root_rot)

        self.planned_motion_ids = torch.arange(self.num_envs, dtype=torch.long, device=self.device)
        self.planned_motion_times = torch.zeros_like(self.motion_times)

        self.planned_motion = EasyDict({
            "global_trans": torch.zeros((self.num_envs, self.fut_frame, 31, 3), dtype=torch.float32, device=self.device),
            "global_rot_quat": torch.zeros((self.num_envs, self.fut_frame, 31, 4), dtype=torch.float32, device=self.device),
            "global_root_vel": torch.zeros((self.num_envs, self.fut_frame, 3), dtype=torch.float32, device=self.device),
            "global_root_ang_vel": torch.zeros((self.num_envs, self.fut_frame, 3), dtype=torch.float32, device=self.device),
            "global_ang_vel": torch.zeros((self.num_envs, self.fut_frame, 31, 3), dtype=torch.float32, device=self.device),
            "global_vel": torch.zeros((self.num_envs, self.fut_frame, 31, 3), dtype=torch.float32, device=self.device),
            "dof_pos": torch.zeros((self.num_envs, self.fut_frame, 30), dtype=torch.float32, device=self.device),
            "dof_vel": torch.zeros((self.num_envs, self.fut_frame, 30), dtype=torch.float32, device=self.device),
            "fps": torch.zeros((self.num_envs, self.fut_frame), dtype=torch.float32, device=self.device),
            })

        self.planned_global_root_trans = torch.zeros((self.num_envs, 2), dtype=torch.float32, device=self.device)
        self.planned_global_root_offset = torch.zeros((self.num_envs, 2), dtype=torch.float32, device=self.device)

        self.past_global_root_height = torch.zeros((self.num_envs, self.past_frame, 1), dtype=torch.float32, device=self.device)
        self.past_delta_root_trans = torch.zeros((self.num_envs, self.past_frame, 2), dtype=torch.float32, device=self.device)
        self.past_whole_body_rot = torch.zeros((self.num_envs, self.past_frame, 15, 4), dtype=torch.float32, device=self.device)

    def reset_idx(self, env_ids, init=False):
        super().reset_idx(env_ids, init)
        # once the robot is reset, we should replan the motion to avoid state mismatch
        if len(env_ids) == 0:
            return
        env_ids = env_ids.view(-1)
        init_motion_ids = self.motion_ids[env_ids, None].repeat(1, self.past_frame).view(-1)
        init_motion_times = self.motion_times[env_ids, None] - self.planning_dt * torch.arange(self.past_frame, device=self.device)[None, :]
        motion_time_spans = self.motion_lib.get_motion_tick_spans(init_motion_ids)
        init_motion_times = torch.minimum(init_motion_times.view(-1), motion_time_spans)
        init_ref_motion = self.motion_lib.get_motion_state(init_motion_ids, init_motion_times)
        # update the ref motion times after the initialization for proper robot reset
        # self.motion_times += self.planning_dt * self.past_frame
        # self.motion_times = torch.fmod(self.motion_times, self.motion_lib.get_motion_tick_spans(self.motion_ids))

        past_ref_root_pos = init_ref_motion.rb_pos[:, 0].clone().reshape(len(env_ids), self.past_frame, 3)
        past_ref_root_vel = init_ref_motion.rb_vel[:, 0].clone().reshape(len(env_ids), self.past_frame, 3)

        past_ref_root_rot = init_ref_motion.rb_rot[:, 0].clone().reshape(len(env_ids), self.past_frame, 4)
        past_ref_dof_pos = init_ref_motion.dof_pos.clone()
        past_ref_local_body_rot = dof_to_obs(past_ref_dof_pos, self.mp_cfg.dof_obs_size, self.mp_cfg.dof_group_offsets,
                                            self.mp_cfg.dof_to_group_reindex, self.mp_cfg.dof_group_types, rot_repr="quat").reshape(len(env_ids), self.past_frame, 14, 4)
        past_ref_whole_body_rot = torch.cat([past_ref_root_rot[:, :, None], past_ref_local_body_rot], dim=2)
        
        self.past_global_root_height[env_ids] = past_ref_root_pos[:, :, 2:3].clone()
        self.past_delta_root_trans[env_ids] = -past_ref_root_vel[:, :, :2] * self.dt * self.planning_interval_step
        self.past_whole_body_rot[env_ids] = past_ref_whole_body_rot.clone()

        # store the denoised raw motion from the diffusion model
        self.keyframe_offset = self.fut_frame // 2  # specified as the centric fut frame, need to be fixed.
        fut_motion_ids = self.motion_ids[env_ids, None].repeat(1, self.keyframe_offset).view(-1)
        fut_keyframe_times = self.motion_times[env_ids, None] + self.planning_dt * torch.arange(self.keyframe_offset, device=self.device)[None, :]
        fut_keyframe_times = torch.minimum(fut_keyframe_times.view(-1), self.motion_lib.get_motion_tick_spans(fut_motion_ids))
        fut_keyframe_motion = self.motion_lib.get_motion_state(fut_motion_ids, fut_keyframe_times)

        past_motion, fut_keyframe_root_trans, fut_keyframe_facing_quat, fut_keyframe_local_body_pos =\
              self.aggregate_motion_conditions(env_ids, fut_keyframe_motion)
        # recalculate the root trans when init
        # fut_keyframe_root_pos = fut_keyframe_motion.rb_pos[:, 0].clone()
        # fut_keyframe_root_pos[:, [0, 1]] -= past_ref_root_pos[:, -1, [0, 1]]
        # fut_keyframe_root_trans = fut_keyframe_root_pos[..., [0, 1]]

        denoised_fut_motion = self.inference_dmp(past_motion, fut_keyframe_root_trans, 
                                                      fut_keyframe_facing_quat, fut_keyframe_local_body_pos,
                                                      guidance_scale=self.guidance_scale)
        motion_dict = self.decode_planned_motion(denoised_fut_motion.permute(0, 3, 1, 2))
        
        for key, value in motion_dict.items():
            if value.shape[0] == len(env_ids):
                self.planned_motion[key][env_ids] = value.clone()  # Use clone to avoid shared references
            else:
                print(f"Warning: Shape mismatch for key {key}. Expected first dim {len(env_ids)}, got {value.shape[0]}")

        # init the planned motion times
        self.planned_motion_times[env_ids] = torch.zeros_like(self.motion_times[env_ids])

        cur_global_root_pos = self.root_states[env_ids, :3]
        self.planned_global_root_trans[env_ids] = cur_global_root_pos[:, :2].clone()
        self.planned_global_root_offset[env_ids] = cur_global_root_pos[:, :2].clone()

        return

    def next_planned_motion_ids(self, env_ids, num_future_steps=1, num_interval_steps=1):
        motion_ids, motion_times = self.planned_motion_ids[env_ids], self.planned_motion_times[env_ids]
        time_offsets = self.dt * (1 + torch.arange(num_future_steps, device=self.device) * num_interval_steps)
        next_planned_motion_times = time_offsets[None, ] + motion_times[:, None]  # (n_envs, fut_motion_steps)
        next_planned_motion_ids = motion_ids.repeat(num_future_steps)
        next_planned_motion_times = torch.minimum(next_planned_motion_times.view(-1), 
                                                  self.planned_motion_span.repeat(next_planned_motion_ids.shape[0]))
        return next_planned_motion_ids, next_planned_motion_times

    def update_planned_motion_obs(self):
        env_ids = torch.arange(self.num_envs, device=self.device)
        # need to consider the fut horizon/gaps
        next_planned_motion_ids, next_planned_motion_times = self.next_planned_motion_ids(env_ids, self.num_future_steps, self.num_interval_steps)
        next_planned_motion_state = self.get_planned_motion_state(next_planned_motion_ids, next_planned_motion_times)

        # extract the cmd/task obs from the planned motion
        next_planned_delta_root_pos = next_planned_motion_state.delta_rb_pos[:, 0].clone()
        next_planned_root_rot = next_planned_motion_state.rb_rot[:, 0].clone()
        next_planned_root_vel = next_planned_motion_state.rb_vel[:, 0].clone()
        next_planned_root_ang_vel = next_planned_motion_state.rb_ang_vel[:, 0].clone()
        next_planned_kb_delta_pos = next_planned_motion_state.delta_rb_pos[:, self.ref_kb_indices].clone()
        next_planned_kb_vel = next_planned_motion_state.rb_vel[:, self.ref_kb_indices].clone()

        # extract the root commands
        next_planned_root_height = next_planned_motion_state.delta_rb_pos[:, 0, 2:3].clone()
        next_planned_proj_grav = quat_rotate_inverse(next_planned_root_rot, self.gravity_vec.repeat(self.num_future_steps, 1))
        next_planned_facing_vec = quat_rotate(next_planned_root_rot, self.forward_vec.repeat(self.num_future_steps, 1))

        # extract dof command
        next_planned_dof_pos = next_planned_motion_state.dof_pos[:, self.ref_dof_indices].clone()
        next_dof_pos_diff = next_planned_dof_pos - self.dof_pos.clone().repeat(self.num_future_steps, 1)
        next_planned_dof_pos -= self.default_dof_pos_all.repeat(self.num_future_steps, 1)

        # extract planned local keypoint pos command
        next_planned_local_kb_pos = transform_global_to_local(next_planned_kb_delta_pos, next_planned_delta_root_pos, 
                                                              next_planned_root_rot).view(self.num_envs*self.num_future_steps, -1)
        next_planned_local_kb_diff = (next_planned_local_kb_pos - 
                                      self.robot_local_kb_pos.view(self.num_envs, -1).repeat(self.num_future_steps, 1))
        
        if self.cfg.env.relative_to_root:
            # relative facing vec to the cur robot base
            cur_root_facing_vec = quat_rotate(self.base_quat, self.forward_vec).repeat(self.num_future_steps, 1)
            next_planned_facing_vec = next_planned_facing_vec - cur_root_facing_vec

            cur_root_rot = self.base_quat.repeat(self.num_future_steps, 1)
            cur_root_vel = self.root_states[:, 7:10].repeat(self.num_future_steps, 1)
            next_planned_local_root_vel = quat_rotate_inverse(cur_root_rot, next_planned_root_vel - cur_root_vel)  # (n_env*n_steps, 3)
            cur_root_ang_vel = self.root_states[:, 10:13].repeat(self.num_future_steps, 1)
            next_planned_local_root_ang_vel = quat_rotate_inverse(cur_root_rot, next_planned_root_ang_vel - cur_root_ang_vel)  # (n_env*n_steps, 3)

            cur_robot_kb_vel = self.rigid_body_states[:, self.key_body_indices, 7:10].clone().repeat(self.num_future_steps, 1, 1)
            next_planned_local_kb_vel = (next_planned_kb_vel - cur_robot_kb_vel).view(self.num_envs*self.num_future_steps*len(self.key_body_indices), -1)
            next_planned_local_kb_vel = (quat_rotate_inverse(cur_root_rot.repeat(len(self.key_body_indices), 1), next_planned_local_kb_vel)
                                        .view(self.num_envs*self.num_future_steps, -1))
        else:
            cur_planned_facing_vec = quat_rotate(self.cur_planned_root_rot, self.forward_vec).repeat(self.num_future_steps, 1)
            next_planned_facing_vec = next_planned_facing_vec - cur_planned_facing_vec

            next_planned_local_root_vel = quat_rotate_inverse(next_planned_root_rot, next_planned_root_vel)
            next_planned_local_root_ang_vel = quat_rotate_inverse(next_planned_root_rot, next_planned_root_ang_vel)

            next_planned_local_kb_vel = (quat_rotate_inverse(next_planned_root_rot.repeat(len(self.key_body_indices), 1), next_planned_kb_vel.view(-1, 3))
                                        .view(self.num_envs*self.num_future_steps, -1))
        
        self.planned_observation_buf[:] = torch.cat([
            next_planned_root_height, 
            next_planned_proj_grav, 
            next_planned_facing_vec, 
            next_planned_local_root_vel * self.obs_scales.lin_vel,
            next_planned_local_root_ang_vel * self.obs_scales.ang_vel, 
            next_planned_dof_pos * self.obs_scales.dof_pos,
            next_dof_pos_diff * self.obs_scales.dof_pos,
            next_planned_local_kb_pos,
            next_planned_local_kb_vel,
        ], dim=-1).view(self.num_envs, self.num_future_steps, -1)

        # get the planned motion frame at current step for motion tracking/rewarding
        cur_planned_motion = self.get_planned_motion_state(self.planned_motion_ids, self.planned_motion_times)
        cur_planned_delta_root_pos = cur_planned_motion.delta_rb_pos[:, 0].clone()
        cur_planned_root_rot = cur_planned_motion.rb_rot[:, 0].clone()
        cur_planned_root_vel = cur_planned_motion.rb_vel[:, 0].clone()
        cur_planned_delta_kb_pos = cur_planned_motion.delta_rb_pos[:, self.ref_kb_indices].clone()
        cur_planned_dof_pos = cur_planned_motion.dof_pos[:, self.ref_dof_indices].clone()
        # read the current planned motion into the buffer
        self.cur_planned_root_height[:] = cur_planned_delta_root_pos[:, 2:3]
        self.cur_planned_delta_root_pos[:] = cur_planned_delta_root_pos
        self.cur_planned_root_rot[:] = cur_planned_root_rot
        self.cur_planned_root_vel[:] = cur_planned_root_vel
        self.cur_planned_root_ang_vel[:] = cur_planned_motion.rb_ang_vel[:, 0].clone()
        self.cur_planned_dof_pos[:] = cur_planned_dof_pos
        self.cur_planned_dof_vel[:] = cur_planned_motion.dof_vel[:, self.ref_dof_indices].clone()
        self.cur_planned_local_kb_pos[:] = transform_global_to_local(cur_planned_delta_kb_pos, cur_planned_delta_root_pos, 
                                                                      cur_planned_root_rot)
        self.cur_planned_kb_vel[:] = cur_planned_motion.rb_vel[:, self.ref_kb_indices].clone()
        self.cur_planned_kb_ang_vel[:] = cur_planned_motion.rb_ang_vel[:, self.ref_kb_indices].clone()
        # TODO: need to add the predicted contact profile from the diffusion backbone ???

        # extract the root command from the planned motion
        self.planned_commands[:, 0:2] = next_planned_local_root_vel.view(self.num_envs, self.num_future_steps, -1)[:, 0, 0:2]
        self.planned_commands[:, 2] = torch.sin(self.yaw - self.planned_yaw)
        self.planned_commands[:, 3] = torch.cos(self.yaw - self.planned_yaw)
        self.planned_commands[:, 0] *= torch.abs(self.planned_commands[..., 0]) > self.cfg.commands.lin_vel_clip
        self.planned_commands[:, 1] *= torch.abs(self.planned_commands[..., 1]) > self.cfg.commands.lin_vel_clip

    def _update_planned_goals(self):
        reset_mask = self.episode_length_buf % (self.cfg.motion_lib.global_trans_reset_time // self.dt) == 0
        self.planned_global_root_trans[reset_mask] = self.root_states[reset_mask, :2].clone()
        # update the global trans odometer according to the ref motion
        self.planned_global_root_trans += self.cur_planned_root_vel[:, :2] * self.dt
        _, _, self.planned_yaw = euler_from_quaternion(self.cur_planned_root_rot)

    def compute_state_observation(self):
        proj_grav = quat_rotate_inverse(self.base_quat, self.gravity_vec)
        if self.cfg.commands.resample:
            root_command = self.commands
        else:
            root_command = self.planned_commands
        
        cur_ref_dof_pos = self.cur_planned_dof_pos.clone() if self.cfg.control.control_type == "residual_pos"\
              else self.default_dof_pos_all

        return torch.cat((
            root_command,
            self.base_ang_vel * self.obs_scales.ang_vel,  # 3
            proj_grav, # 3
            (self.dof_pos - cur_ref_dof_pos) * self.obs_scales.dof_pos,
            self.dof_vel * self.obs_scales.dof_vel,
            self.action_history_buf[:, -1]), dim=-1)
    
    def compute_observations(self):
        state_buf = self.compute_state_observation()
        if self.cfg.noise.add_noise and self.headless:
            state_buf += (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec * min(
                self.total_env_steps_counter / (self.cfg.noise.noise_increasing_steps * 24), 1.)
        elif self.cfg.noise.add_noise and not self.headless:
            state_buf += (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec
        else:
            state_buf += 0.

        if self.cfg.domain_rand.domain_rand_general and self.cfg.env.n_priv_latent > 0:
            priv_latent = torch.cat((
                self.mass_params_tensor,
                self.env_frictions,
                self.motor_strength[0] - 1,
                self.motor_strength[1] - 1,
                self.joint_friction_coeffs,
                self.joint_damping_coeffs,
                self.joint_armature_coeffs,
            ), dim=-1)
        else:
            priv_latent = torch.zeros((self.num_envs, self.cfg.env.n_priv_latent), device=self.device)
        
        if self.cfg.env.n_priv > 0:
            root_facing_vec = quat_rotate(self.base_quat, self.forward_vec)
            feet_contact = self.contact_filt.float() - 0.5
            priv_explicit = torch.cat((
                self.root_states[:, 2:3],
                root_facing_vec,
                feet_contact,
                self.base_lin_vel * self.obs_scales.lin_vel,
                self.robot_local_kb_pos.view(self.num_envs, -1),
            ), dim=-1)
        else:
            priv_explicit = torch.zeros((self.num_envs, self.cfg.env.n_priv), device=self.device)

        obs_planner = self.planned_observation_buf.view(self.num_envs, -1)

        self.obs_buf = torch.cat([state_buf, obs_planner, priv_explicit, priv_latent,
                                   self.obs_history_buf.view(self.num_envs, -1)], dim=-1)

        if self.cfg.env.history_len > 0:
            self.obs_history_buf = torch.where(
                (self.episode_length_buf <= 1)[:, None, None],
                torch.stack([state_buf] * self.cfg.env.history_len, dim=1),
                torch.cat([
                    self.obs_history_buf[:, 1:],
                    state_buf.unsqueeze(1)
                ], dim=1)
            )
        self.contact_buf = torch.where(
            (self.episode_length_buf <= 1)[:, None, None],
            torch.stack([self.contact_filt.float()] * self.cfg.env.contact_buf_len, dim=1),
            torch.cat([
                self.contact_buf[:, 1:],
                self.contact_filt.float().unsqueeze(1)
            ], dim=1)
        )

    def post_physics_step(self):
        Humanoid.post_physics_step(self)
        # step motion lib
        self.motion_times += self.dt
        self.motion_times = torch.fmod(self.motion_times, self.motion_lib.get_motion_tick_spans(self.motion_ids))
        self._update_goals()
        self.update_ref_motion_obs()

        # update past motion deque and then step the diffusion motion planner periodically
        if self.global_counter % self.planning_interval_step == 0:
            if self.inference_type == "open_loop":
                # update past whole body rot
                past_root_rot = self.cur_planned_root_rot.clone()
                past_dof_pos = self.cur_planned_dof_pos.clone()
                # update past root trans
                past_global_root_height = self.cur_planned_root_height.clone()
                past_delta_root_trans = -self.cur_planned_root_vel[:, :2].clone() * self.dt * self.planning_interval_step
            elif self.inference_type == "closed_loop":
                # update past whole body rot
                past_root_rot = self.root_states[:, 3:7].clone()
                past_dof_pos = self.dof_pos.clone()
                # update past root trans
                past_global_root_height = self.root_states[:, 2:3].clone()
                # update past delta root trans
                past_delta_root_trans = -self.root_states[:, 7:9].clone() * self.dt * self.planning_interval_step
            
            pseudo_past_dof_pos = torch.zeros((self.num_envs, self.robot_fk.num_dof), dtype=torch.float32, device=self.device)
            pseudo_past_dof_pos[:, self.ref_dof_indices] = past_dof_pos
            past_ref_local_body_rot = dof_to_obs(pseudo_past_dof_pos, self.mp_cfg.dof_obs_size, self.mp_cfg.dof_group_offsets,
                                            self.mp_cfg.dof_to_group_reindex, self.mp_cfg.dof_group_types, rot_repr="quat").reshape(self.num_envs, 14, 4)
            past_whole_body_rot = torch.cat([past_root_rot[:, None], past_ref_local_body_rot], dim=1)
            
            
            # # hard encode here, need to modify later.
            # euler_orders = [['Y', 'X', 'Z'], ['Y'], ['Y', 'X'], ['Y'], ['Y', 'X', 'Z'], ['Y'], ['Y', 'X'], ['Y'], 
            #                 ['Z', 'X'], ["Z", "Y"],
            #                 ['Y', 'X', 'Z'], ['Y', 'Z'], ['Y', 'X', 'Z'], ['Y', 'Z']]
            # sub_dof_28_idx = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 
            #                   14, 15, 16, 17,
            #                   18, 19, 20, 21, 22, 24, 25, 26, 27, 28]  # discard the wrist dofs.
            # past_local_joints_rot_matrix = get_local_rot_matrix_from_joint_pos(pseudo_past_dof_pos[:, sub_dof_28_idx], euler_orders)
            # past_local_joints_rot_quat = wxyz_to_xyzw(matrix_to_quaternion(past_local_joints_rot_matrix))
            # past_whole_body_rot = torch.cat([past_root_rot[:, None], past_local_joints_rot_quat], dim=1)

            
            self.past_whole_body_rot = torch.cat([self.past_whole_body_rot[:, 1:], past_whole_body_rot[:, None]], dim=1)
            self.past_global_root_height = torch.cat([self.past_global_root_height[:, 1:], past_global_root_height[:, None]], dim=1)
            self.past_delta_root_trans = torch.cat([self.past_delta_root_trans[:, 1:], past_delta_root_trans[:, None]], dim=1)  # (env, horizon, 2)
        
        # manage the planning motion times
        self.planned_motion_times += self.dt
        replan_mask = self.planned_motion_times >= (self.planned_motion_span
                                                   - self.dt * self.num_future_steps * self.num_interval_steps)
        # step the diffuson model model to replan
        if replan_mask.sum() > 0:
            replan_id = torch.nonzero(replan_mask, as_tuple=True)[0]
            replan_motion_ids = self.motion_ids[replan_id, None].repeat(1, self.keyframe_offset).view(-1)
            fut_keyframe_times = self.motion_times[replan_id, None] + self.planning_dt * torch.arange(self.keyframe_offset, device=self.device)[None, :]
            fut_keyframe_times = torch.minimum(fut_keyframe_times.view(-1), self.motion_lib.get_motion_tick_spans(replan_motion_ids))
            fut_keyframe_motion = self.motion_lib.get_motion_state(replan_motion_ids, fut_keyframe_times)
            
            past_motion, fut_keyframe_root_trans, fut_keyframe_facing_quat, fut_keyframe_local_body_pos =\
                  self.aggregate_motion_conditions(replan_id, fut_keyframe_motion)
            denoised_fut_motion = self.inference_dmp(past_motion, fut_keyframe_root_trans, 
                                                    fut_keyframe_facing_quat, fut_keyframe_local_body_pos, 
                                                    guidance_scale=self.guidance_scale)
            motion_dict = self.decode_planned_motion(denoised_fut_motion.permute(0, 3, 1, 2))
            
            # blend the end and start of the planned rollout for smooth motion transition
            num_transition_frames = round(self.dt * self.num_future_steps * self.num_interval_steps / self.planning_dt)
            for key, value in motion_dict.items():
                if key in ["dof_pos", "dof_vel", "rb_vel", "rb_ang_vel"]:
                    value[:, :num_transition_frames] = (0.5 * self.planned_motion[key][replan_mask, -num_transition_frames:] 
                                                          + 0.5 * value[:, :num_transition_frames])
                if key in ["rb_rot"]:
                    value[:, :num_transition_frames] = torch_utils.slerp(self.planned_motion[key][replan_mask, -num_transition_frames:], 
                                                                        value[:, :num_transition_frames], 0.5)
            
            # update the planned motion
            for key, value in motion_dict.items():
                self.planned_motion[key][replan_id] = value.clone()

            # update the planned_global_root_offset when replanning
            self.planned_global_root_offset[replan_id] = self.planned_global_root_trans[replan_id].clone()
            # reset the planning motion times after replanning
            self.planned_motion_times[replan_id] = self.dt
        
        self._update_planned_goals()
        self.update_planned_motion_obs()

        if self.viewer and self.enable_viewer_sync and self.debug_viz:
            if self.cfg.motion_lib.motion_sync:
                self._planned_motion_sync()
                self.gym.refresh_actor_root_state_tensor(self.sim)
                self.gym.refresh_net_contact_force_tensor(self.sim)
                self.gym.refresh_rigid_body_state_tensor(self.sim)
                self.gym.refresh_force_sensor_tensor(self.sim)
            self.gym.clear_lines(self.viewer)
            self.draw_key_bodies_demo()
            self.draw_key_bodies_planned()

        return
    
    def aggregate_motion_conditions(self, replan_mask, fut_keyframe_motion):
        env_ids = replan_mask.view(-1)
        num_envs = env_ids.shape[0]

        fut_keyframe_root_vel = fut_keyframe_motion.rb_vel.reshape(num_envs, self.keyframe_offset, 31, -1)[:, :, 0].clone()
        fut_keyframe_root_trans = torch.cumsum(fut_keyframe_root_vel * self.planning_dt, dim=1)[:, -1, :2]

        fut_keyframe_rb_pos = fut_keyframe_motion.rb_pos.reshape(num_envs, self.keyframe_offset, 31, -1)[:, -1].clone()
        fut_facing_vec = extract_facing_vector(fut_keyframe_rb_pos)
        fut_facing_quat = extract_traj_quat_with_facing_vec(fut_facing_vec.cpu().numpy())
        fut_keyframe_facing_quat = torch.from_numpy(fut_facing_quat.astype(np.float32)).to(self.device)
        fut_keyframe_facing_quat = get_rotation(fut_keyframe_facing_quat[..., [3, 0, 1, 2]], rot_req="6d")

        fut_keyframe_track_body_pos = fut_keyframe_rb_pos[:, self.condition_kb_indices].clone() # (1*T, 12, 3)
        ref_root_pos = fut_keyframe_rb_pos[:, 0].clone()
        fut_keyframe_rb_rot = fut_keyframe_motion.rb_rot.reshape(num_envs, self.keyframe_offset, 31, -1)[:, -1].clone()
        ref_root_rot = fut_keyframe_rb_rot[:, 0].clone()
        fut_keyframe_local_body_pos = transform_global_to_local(fut_keyframe_track_body_pos, ref_root_pos, ref_root_rot)

        past_whole_body_rot = self.past_whole_body_rot[env_ids].clone()
        past_whole_body_rot_6d = get_rotation(past_whole_body_rot[..., [3, 0, 1, 2]], rot_req="6d")

        past_delta_root_trans = self.past_delta_root_trans[env_ids].clone()
        past_global_root_trans = torch.cumsum(past_delta_root_trans.flip(1), dim=1).flip(1)
        past_global_root_trans[:, -1] = 0
        past_global_root_height = self.past_global_root_height[env_ids].clone()
        past_global_root_pos = torch.cat([past_global_root_trans, past_global_root_height], dim=-1)
        root_pos_pads = torch.zeros_like(past_global_root_pos)
        past_global_root_pos = torch.cat([past_global_root_pos, root_pos_pads], dim=-1)
        past_motion = torch.cat([past_whole_body_rot_6d, past_global_root_pos.unsqueeze(-2)], dim=-2)

        return past_motion, fut_keyframe_root_trans, fut_keyframe_facing_quat, fut_keyframe_local_body_pos

    @torch.no_grad()
    def inference_dmp(self, past_motion, fut_keyframe_root_trans, fut_keyframe_facing_quat,
                       fut_keyframe_local_body_pos, guidance_scale=1.0):
        bs, n_frames, n_joints, feat = past_motion.shape
        x_t = torch.randn(bs, self.mp_cfg.arch.njoints, 6, self.fut_frame, device=past_motion.device)

        traj_trans = torch.zeros_like(fut_keyframe_root_trans)[:, None].repeat(1, self.fut_frame, 1)
        traj_trans[:, (self.fut_frame//2)-1] = fut_keyframe_root_trans
        traj_pose = torch.zeros_like(fut_keyframe_facing_quat)[:, None].repeat(1, self.fut_frame, 1)
        traj_pose[:, (self.fut_frame//2)-1] = fut_keyframe_facing_quat
        fut_local_track_body_pos = torch.zeros_like(fut_keyframe_local_body_pos)[:, None].repeat(1, self.fut_frame, 1, 1)
        fut_local_track_body_pos[:, (self.fut_frame//2)-1] = fut_keyframe_local_body_pos

        for t in range(self.mp_cfg.diff.diffusion_steps-1, -1, -1):
            cond = {
                "time_steps": t*torch.ones((bs, 1), device=past_motion.device, dtype=torch.long),
                "past_motion": past_motion.permute(0, 2, 3, 1),
                "traj_trans": traj_trans.permute(0, 2, 1),
                "traj_pose": traj_pose.permute(0, 2, 1),
                "fut_local_track_body_pos": fut_local_track_body_pos.permute(0, 2, 3, 1),
            }
            # seperate the upper and lower body pos as two chuncks of tokens
            upper_kb_idx = [0, 1, 2, 3, 4, 5, 6]
            lower_kb_idx = [7, 8, 9, 10, 11]
            upper_kb_mask = torch.rand(bs, device=past_motion.device) < 0
            lower_kb_mask = torch.rand(bs, device=past_motion.device) < 0  # mask out lower or upper body
            upper_kb_condition =  cond["fut_local_track_body_pos"].clone()
            upper_kb_condition[:, lower_kb_idx, :, :n_frames//2] *= lower_kb_mask.view(bs, 1, 1, 1)
            lower_kb_condition = cond["fut_local_track_body_pos"].clone()
            lower_kb_condition[:, upper_kb_idx, :, :n_frames//2] *= upper_kb_mask.view(bs, 1, 1, 1)

            batch_t = torch.ones((bs, ), device=past_motion.device, dtype=torch.long) * t
            x_0 = self.dmp_model.forward(x_t, self.mp_diffuser._scale_timesteps(batch_t), cond["past_motion"], cond["traj_pose"],
                                    cond["traj_trans"], upper_kb_condition, lower_kb_condition)
            if guidance_scale != 1.0:
                x_0_uncond = self.dmp_model.forward(x_t, self.mp_diffuser._scale_timesteps(batch_t), torch.zeros_like(cond["past_motion"]), cond["traj_pose"],
                                        cond["traj_trans"], upper_kb_condition, lower_kb_condition)
                x_0 = x_0_uncond + guidance_scale * (x_0 - x_0_uncond)

            model_log_variance = self.mp_diffuser.posterior_log_variance_clipped[t]
            mean_coef1 = self.mp_diffuser.posterior_mean_coef1[t]
            mean_coef2 = self.mp_diffuser.posterior_mean_coef2[t]
            model_mean = mean_coef1 * x_0 + mean_coef2 * x_t

            if t > 0:
                noise = torch.randn(bs, self.mp_cfg.arch.njoints, 6, self.fut_frame, device=past_motion.device)
                x_t = model_mean + torch.exp(0.5 * torch.tensor(model_log_variance, dtype=torch.float32,
                                                                device=past_motion.device)) * noise
            else:
                x_t = model_mean

        return x_t.detach()
    
    def decode_planned_motion(self, denoised_fut_motion):
        n_envs, n_frames, n_joints, feat = denoised_fut_motion.shape
        planned_delta_root_pos = denoised_fut_motion[:, :, -1, :3]
        local_body_rot_6d = denoised_fut_motion[:, :, :-1].reshape(n_envs*n_frames, n_joints-1, 6)
        planned_body_rot_quat = repr6d2quat(local_body_rot_6d)[..., [1, 2, 3, 0]].reshape(n_envs, n_frames, n_joints-1, 4)  # (env, fut_frame, 15, 4)
        
        assert len(planned_body_rot_quat.shape) == 4
        planned_body_rot_quat_list = [planned_body_rot_quat[:, :, i] for i in range(self.num_dof_groups)]
        ignored_body_idx = [13, 16]
        for j in ignored_body_idx:
            planned_body_rot_quat_list.insert(j, torch.tensor([0, 0, 0, 1.], device=self.device).expand(n_envs, n_frames, 4))
        planned_body_rot_quat = torch.stack(planned_body_rot_quat_list, dim=-2).cpu().numpy()

        pose_aa_sk = sRot.from_quat(planned_body_rot_quat.reshape(-1, 4)).as_rotvec().reshape(n_envs*n_frames, n_joints+1, 3)
        pose_aa_sk = torch.from_numpy(pose_aa_sk).to(self.device).float()
        pose_aa_robot = torch.zeros((pose_aa_sk.shape[0], self.robot_fk.num_dof, 3), dtype=torch.float32, device=self.device)
        for i in range(len(self.robot_dof_offset) - 1):
            sub_joints = self.robot_dof_offset[i + 1] - self.robot_dof_offset[i]
            for j in range(sub_joints):
                pose_aa_robot[:, self.robot_dof_offset[i] + j] = pose_aa_sk[:, 1 + i] * \
                                                            self.robot_fk.dof_axis[self.robot_dof_offset[i] + j][None,].to(self.device)
        pose_aa_robot = torch.cat([pose_aa_sk[:, 0:1], pose_aa_robot], dim=1).reshape(n_envs, n_frames, self.robot_fk.num_dof+1, 3)         
        # perform fk for unified ref motion
        motion_dict = self.robot_fk.fk_batch(planned_delta_root_pos.cpu(), pose_aa_robot.cpu(), return_full=True, fps=int(1/self.planning_dt))
        motion_dict.update({key: value.to(self.device) for key, value in motion_dict.items()})
        print(self.global_counter)
        print("the diffusion motion planner is updated at: ", self.episode_length_buf[0])
        return motion_dict
    
    def get_planned_motion_state(self, motion_ids, motion_times, full_state=False):
        """
        The full keys of the planned motion dict:
        'global_trans', 'global_rot_quat', 
        'global_root_vel', 'global_root_ang_vel', 'global_ang_vel', 
        'global_vel', 'dof_vel', 'dof_pos'
        """
        phase = motion_times / self.planned_motion_span
        start_frames = (phase * (self.fut_frame-1)).long()
        end_frames = torch.minimum(start_frames+1, torch.ones_like(start_frames) * (self.fut_frame-1))
        blend = motion_times / self.planning_dt - start_frames
        blend = blend[:, None]  # extend for feat dim
        blend_exp = blend[:, None]  # extend for body dim

        delta_trans0 = self.planned_motion.global_trans[motion_ids, start_frames]
        delta_trans1 = self.planned_motion.global_trans[motion_ids, end_frames]
        delta_translations = delta_trans0 * (1 - blend_exp) + delta_trans1 * blend_exp
        global_vel0 = self.planned_motion.global_vel[motion_ids, start_frames]
        global_vel1 = self.planned_motion.global_vel[motion_ids, end_frames]
        global_velocities = global_vel0 * (1 - blend_exp) + global_vel1 * blend_exp
        global_ang_vel0 = self.planned_motion.global_ang_vel[motion_ids, start_frames]
        global_ang_vel1 = self.planned_motion.global_ang_vel[motion_ids, end_frames]
        global_ang_velocities = global_ang_vel0 * (1 - blend_exp) + global_ang_vel1 * blend_exp

        dof_pos0 = self.planned_motion.dof_pos[motion_ids, start_frames]
        dof_pos1 = self.planned_motion.dof_pos[motion_ids, end_frames]
        dof_pos = dof_pos0 * (1 - blend) + dof_pos1 * blend
        dof_vel0 = self.planned_motion.dof_vel[motion_ids, start_frames]
        dof_vel1 = self.planned_motion.dof_vel[motion_ids, end_frames]
        dof_vel = dof_vel0 * (1 - blend) + dof_vel1 * blend

        global_rot0 = self.planned_motion.global_rot_quat[motion_ids, start_frames]
        global_rot1 = self.planned_motion.global_rot_quat[motion_ids, end_frames]
        global_rot = torch_utils.slerp(global_rot0, global_rot1, blend_exp)

        motion_state = EasyDict({
            "dof_pos": dof_pos,
            "dof_vel": dof_vel,
            "delta_rb_pos": delta_translations,
            "rb_rot": global_rot,
            "rb_vel": global_velocities,
            "rb_ang_vel": global_ang_velocities,
            })
        return motion_state
    
    def draw_key_bodies_planned(self):
        keypoint_geom = gymutil.WireframeSphereGeometry(0.06, 32, 32, None, color=(1, 0, 0))
        cur_planned_local_kb_pos = self.cur_planned_local_kb_pos.clone()
        if self.cfg.motion_lib.global_tracking:
            global_root_trans = self.planned_global_root_trans[:].clone()
        else:
            global_root_trans = self.root_states[:, 0:2].clone()
        global_root_pos = torch.cat([global_root_trans, self.cur_planned_root_height], dim=-1)
        global_root_rot = self.cur_planned_root_rot.clone()
        cur_planned_kb_pos = transform_local_to_global(cur_planned_local_kb_pos, global_root_pos, global_root_rot)

        for i in range(len(self.ref_kb_indices)):
            pose = gymapi.Transform(
                gymapi.Vec3(cur_planned_kb_pos[self.lookat_id, i, 0], cur_planned_kb_pos[self.lookat_id, i, 1],
                            cur_planned_kb_pos[self.lookat_id, i, 2]), r=None)
            gymutil.draw_lines(keypoint_geom, self.gym, self.viewer, self.envs[self.lookat_id], pose)

    def _planned_motion_sync(self):
        env_ids = torch.arange(self.num_envs, dtype=torch.long, device=self.device)

        root_pos = self.cur_planned_delta_root_pos.clone()
        root_pos[:, :2] += self.planned_global_root_offset
        root_rot = self.cur_planned_root_rot.clone()
        root_vel = torch.zeros_like(root_pos)
        root_ang_vel = torch.zeros_like(root_pos)

        dof_pos = self.cur_planned_dof_pos.clone()
        dof_vel = torch.zeros_like(dof_pos)

        self._set_env_state(env_ids=env_ids,
                            root_pos=root_pos,
                            root_rot=root_rot,
                            dof_pos=dof_pos,
                            root_vel=root_vel,
                            root_ang_vel=root_ang_vel,
                            dof_vel=dof_vel)

        env_ids_int32 = env_ids.to(dtype=torch.int32)
        self.gym.set_actor_root_state_tensor_indexed(self.sim,
                                                     gymtorch.unwrap_tensor(self.root_states),
                                                     gymtorch.unwrap_tensor(env_ids_int32), len(env_ids_int32))
        self.gym.set_dof_state_tensor_indexed(self.sim,
                                              gymtorch.unwrap_tensor(self.dof_state),
                                              gymtorch.unwrap_tensor(env_ids_int32), len(env_ids_int32))
        return
    
    # ===================================================
    # Reward functions
    # ===================================================

LeftHipIdx, RightHipIdx, LeftShoulderIdx, RightShoulderIdx = 1, 8, 19, 25
FacingVector = [1, 0, 0]


def extract_facing_vector(global_position):
    upper_across = global_position[:, LeftShoulderIdx, :] - global_position[:, RightShoulderIdx, :]
    lower_across = global_position[:, LeftHipIdx, :] - global_position[:, RightHipIdx, :]
    across = (upper_across / torch.sqrt((upper_across ** 2).sum(dim=-1))[..., None] +
              lower_across / torch.sqrt((lower_across ** 2).sum(dim=-1))[..., None])
    across = across / torch.sqrt((across ** 2).sum(dim=-1))[..., None]
    forward_vec = torch.linalg.cross(across, torch.tensor([[0, 0, 1.]], device=across.device))  # noqa
    return forward_vec


def extract_traj_quat_with_facing_vec(forwards):
    facing_vec0 = np.array([FacingVector])
    v0s = facing_vec0.repeat(len(forwards), axis=0)
    xyz = np.cross(v0s, forwards)  # noqa
    w = np.sqrt((v0s ** 2).sum(axis=-1) * (forwards ** 2).sum(axis=-1)) + (v0s * forwards).sum(axis=-1)
    between_xyzw = np.concatenate([xyz, w[..., np.newaxis]], axis=-1)
    # normalize the wxyz quat using sRot.
    between = sRot.from_quat(between_xyzw).as_quat()

    return between


def transform_global_to_local(global_kb_pos, global_root_pos, root_rot, heading_only=False):
    """
    global_kb_pos: The global position of the key body with an extra body dim.
    global_root_pos: The global root position.
    root_rot: The global root rot quat.
    """
    batch, num_key_bodies = global_kb_pos.shape[:2]
    local_kb_pos = global_kb_pos - global_root_pos.unsqueeze(1)  # expand the body dim
    if heading_only:
        root_rot = torch_utils.calc_heading_quat(root_rot)
    head_rot_expand = root_rot.unsqueeze(1).expand(-1, num_key_bodies, -1)
    local_kb_pos = quat_rotate_inverse(head_rot_expand.reshape(-1, 4), local_kb_pos.view(-1, 3)).view(global_kb_pos.shape)
    return local_kb_pos


def transform_local_to_global(local_kb_pos, global_root_pos, root_rot, heading_only=False):
    """
    local_kb_pos: The local position of the key body with an extra body dim.
    global_root_pos: The target global root position.
    root_rot: The target global root rot quat.
    """
    batch, num_key_bodies = local_kb_pos.shape[:2]
    if heading_only:
        root_rot = torch_utils.calc_heading_quat(root_rot)
    heading_rot_expand = root_rot.unsqueeze(1).expand(-1, num_key_bodies, -1)
    global_body_pos = quat_rotate(heading_rot_expand.reshape(-1, 4), local_kb_pos.view(-1, 3)).view(local_kb_pos.shape)
    global_body_pos += global_root_pos[:, None]
    return global_body_pos
