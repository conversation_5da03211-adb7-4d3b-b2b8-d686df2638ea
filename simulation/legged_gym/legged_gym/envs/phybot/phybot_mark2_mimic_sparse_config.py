from legged_gym.envs.phybot.phybot_mark2_mimic_config import PM2MimicCfg
from legged_gym.envs.base.humanoid_config import HumanoidCfg, HumanoidCfgPPO


class PM2MimicSparseCfg(PM2MimicCfg):
    class env(PM2MimicCfg.env):
        num_envs = 4096
        num_dofs = 26
        n_key_bodies = 14
        n_proprio = 4 + 3 + 3 + 3 * num_dofs
        n_priv = 1 + 2 + 3 + n_key_bodies * 3
        n_priv_latent = 4 + 1 + 3 + 2 * num_dofs # 4 + 1 + 3 + 2 * num_dofs 

        enable_height_termination = False
        enable_orient_termination = False
        enable_max_kb_pos_err_cut = True

        termination_dist_curriculum = True
        init_termination_dist = 0.5
        dist_curriculum_scale = -0.25

        height_offset = 0.05

        num_future_steps = 6
        num_interval_steps = 4
        num_task_obs_inst = (1 + 6 + 3 + 3 + num_dofs + 2 * n_key_bodies * 3)
        num_task_observations = num_task_obs_inst * num_future_steps
        history_len = 3
        num_observations = (n_proprio + num_task_observations + n_priv + n_priv_latent + 
                    history_len * n_proprio)

    class rewards(PM2MimicCfg.rewards):
        reward_groups = ["task", "regu", "style"]
        reward_group_weights = [3.0, 0.2, 1.0]
        num_reward_groups = len(reward_groups)  # used for multi-critic creation.

        # represent as sparse keyframe reawrd 
        # class scales:
        #     # base pose
        #     tracking_ref_height = 1.0
        #     tracking_ref_yaw = 1.0
        #     tracking_ref_roll_pitch = 1.0
        #     # upper body posture
        #     tracking_ref_shoulder_pos = 1.0

        class scales:
            tracking_ref_kb_pos = 2.0
            tracking_ref_kb_rot = 2.0
            tracking_ref_height = 3.0
            tracking_ref_roll_pitch = 1.0
            tracking_ref_shoulder_pos = 2.0
            tracking_ref_lower_pos = 5.0
            # tracking_feet_contacts = 2.0
    
    class constraints(PM2MimicCfg.rewards):
        alive = 0.0

        class scales:
            # style reg
            style_tracking_ref_dof_pos = 5.0
            # style_tracking_ref_dof_vel = 1.0
            # style_tracking_ref_lin_vel = 1.0
            # style_tracking_ref_ang_vel = 3.0
            # style_tracking_ref_kb_lin_vel = 3.0
            # style_tracking_ref_kb_ang_vel = 1.0

            # limbs reg
            # regu_feet_stumble = -1.25 * 2
            # regu_feet_contact_forces = -1e-3
            # regu_foot_slip = -0.2
            # regu_collision = -10.0
            # control reg
            regu_dof_pos_limits = -10
            regu_action_rate = -0.1
            regu_energy = -1e-3
            regu_dof_torque_limits = -1.0
            regu_dof_acc = -1e-7
            regu_dof_vel = -1e-5
    
    class asset(PM2MimicCfg.asset):
        terminate_after_contacts_on = []
    
    class motion_lib(PM2MimicCfg.motion_lib):
        motion_sync = False
        motion_dump = "cmu_walk_run500_120fps"
        pre_processed = False
        specified_ids = ["77_16_poses", ]
        num_to_load = -1
        sample_scheme = "dynamic"
        truncate_phase = 0.6
        cyclic_transition = False

        global_trans_reset_time = 0.5

        enable_sparse_keyframe = True
        steps_to_keyframe_range = [4, 10]

        bucket_width = 0.2  # for singe traj tracking

    class domain_rand(PM2MimicCfg.domain_rand):
        domain_rand_general = False  # manually set this, setting from parser does not work;

        # push_robots = (False and domain_rand_general)
        push_robots = False
        push_interval_s = 3
        max_push_vel_xy = 1.0
        max_push_ang_vel = 0.5

        randomize_motor = True
        motor_strength_range = [0.8, 1.2] # [0.8, 1.2]

        action_delay = False
        action_buf_len = 8

        # apply the dynamics randomization follow versatile_loco: https://arxiv.org/abs/2401.16889
        randomize_friction = (True and domain_rand_general)
        friction_range = [0.3, 1.5]

        # base link 
        randomize_base_mass = (True and domain_rand_general)
        added_mass_range = [-4., 4]
        randomize_base_com = (True and domain_rand_general)
        added_com_range = [-0.1, 0.1] 

        randomize_joint_friction = (True and domain_rand_general)
        joint_friction_range = [0.01, 0.2]
        randomize_joint_damping = (True and domain_rand_general)
        joint_damping_range = [0.1, 1.2]
        randomize_joint_armature = (True and domain_rand_general)
        joint_armature_range = [0.008, 0.06]  # target value

        randomize_gravity = (False and domain_rand_general)
        gravity_rand_interval_s = 10
        gravity_range = (-0.1, 0.1)

class PM2MimicSparseCfgPPO(HumanoidCfgPPO):
    seed = 1

    class runner(HumanoidCfgPPO.runner):
        policy_class_name = "ActorMultiCritics"  # 'ActorCriticRMA'
        algorithm_class_name = 'PPO'  #  "PPORMA"
        runner_class_name = 'OnPolicyRunner'
        max_iterations = 20001  # number of policy updates

        # logging
        save_interval = 100  # check for potential saves every this many iterations
        eval_interval = -1
        experiment_name = 'test'
        run_name = ''
        # load and resume
        resume = False
        load_run = -1  # -1 = last run
        checkpoint = -1  # -1 = last saved model
        resume_path = None  # updated from load_run and chkpt

    class policy(HumanoidCfgPPO.policy):
        teacher_policy = True
        actor_hidden_dims = [1024, 512, 256, ]
        critic_hidden_dims = [1024, 512, 256, ]
        action_std = [0.3, 0.3, 0.3, 0.4, 0.2, 0.2, 0.1] * 2 + [0.1, 0.1] + [0.1] * 10

        # MCP-specific configs
        num_prims = 4
        ensemble_type = "mcp"
        composer_hidden_dims = [1024, 512, 256,]

    class algorithm(HumanoidCfgPPO.algorithm):
        grad_penalty_coef_schedual = None
    