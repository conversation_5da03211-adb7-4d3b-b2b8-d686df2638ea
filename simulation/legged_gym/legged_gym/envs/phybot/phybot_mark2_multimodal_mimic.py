import torch
import numpy as np
from .phybot_mark2_mimic import PM2Mimic, transform_local_to_global
from .phybot_mark2_mimic_sparse_config import PM2MimicSparseCfg
from legged_gym.gym_utils.helpers import class_to_dict
from legged_gym.envs.base.humanoid import Humanoid
from enum import Enum
from legged_gym.utils.torch_utils import quat_diff_norm


class PM2MultimodalMimic(PM2Mimic):
    class TrackingModes(Enum):
        """
        # Tracking modes for task object assignment
        Root: trackng the root_command extracted from the ref traj
        Telep: tracking root and upper body ref
        WholeBody: tracking root, upper body and lower body ref
        """
        # the tracking mode
        Root = 0
        Telep = 1
        WholeBody = 2
    
    class InterventionCases(Enum):
        """
        # intervention case for task object adjustment
        Transition: transition from one motion clip to another follow
                the last tracking mode.
        Compliant: Switch to the force(disturibance)-adaptive mode with 
                external applied disturbance.
        """
        Transition = 0
        Compliant = 1
    
    def __init__(self, cfg: PM2MimicSparseCfg, sim_params, physics_engine, sim_device, headless):
        self.cfg = cfg
        self.global_trans_reset_time = cfg.motion_lib.global_trans_reset_time
        self.termination_dist_curriculum = cfg.env.termination_dist_curriculum
        self.use_estimator = (self.cfg.env.n_priv != 0)
        # read the new reward groups 
        Humanoid.__init__(self, cfg, sim_params, physics_engine, sim_device, headless)
        self.last_feet_z = 0.05
        self.episode_length = torch.zeros((self.num_envs), device=self.device)
        self.feet_height = torch.zeros((self.num_envs, 2), device=self.device)
        self.state_init = self.StateInit[cfg.init_state.mode]

        # tracking_mode for multi-modal whole body controller training
        # we pre-allocate the tracking mode for each env and keep it fixed during training
        self.tracking_mode_set = torch.tensor([getattr(self.TrackingModes, mode).value for mode in self.cfg.commands.tracking_modes],
                                               device=self.device)
        self.tracking_mode_probs = torch.tensor(self.cfg.commands.tracking_mode_probs, device=self.device)
        indices = torch.multinomial(self.tracking_mode_probs, num_samples=self.num_envs, replacement=True)
        self.tracking_mode = self.tracking_mode_set[indices]
        self.root_mask = self.tracking_mode == self.TrackingModes.Root.value
        self.telep_mask = self.tracking_mode == self.TrackingModes.Telep.value
        self.wholebody_mask = self.tracking_mode == self.TrackingModes.WholeBody.value

        self._load_motion_lib(cfg.motion_lib)
        self._init_motion_buffer(cfg.motion_lib)
        self.reset_idx(torch.tensor(range(self.num_envs), device=self.device), init=True)

    def _get_body_indices(self):
        super()._get_body_indices()
        key_body_names = self.cfg.asset.key_body_names
        upper_keybody_names = self.cfg.asset.upper_keybody_names
        lower_keybody_names = self.cfg.asset.lower_keybody_names
        base_keybody_names = self.cfg.asset.base_keybody_names
        self.upper_kb_indices = torch.tensor([key_body_names.index(name) for name in upper_keybody_names],
                                             dtype=torch.long, device=self.device)
        self.lower_kb_indices = torch.tensor([key_body_names.index(name) for name in lower_keybody_names],
                                             dtype=torch.long, device=self.device)
        self.base_kb_indices = torch.tensor([key_body_names.index(name) for name in base_keybody_names],
                                             dtype=torch.long, device=self.device)
        self.telep_kb_indices = torch.cat([self.base_kb_indices, self.upper_kb_indices], dim=0)
        self.limbs_kb_indices = torch.cat([self.lower_kb_indices, self.upper_kb_indices], dim=0)
        return

    def check_termination(self):
        # base termination condition
        self.reset_buf = torch.any(torch.norm(self.contact_forces[:, self.termination_contact_indices, :], dim=-1) > 5., dim=1)
        time_out_threshold = torch.maximum(self.motion_lib.get_motion_tick_spans(self.motion_ids), 
                                           torch.ones_like(self.motion_lib.get_motion_tick_spans(self.motion_ids)) * self.cfg.env.episode_length_s)
        if self.cfg.motion_lib.cyclic_transition:
            self.time_out_buf = self.episode_length_buf * self.dt > time_out_threshold
        else:
            self.time_out_buf = self.motion_times > time_out_threshold

        # relaxed termination condition
        height_cutoff = self.root_states[:, 2] < self.cfg.rewards.termination_height
        roll_cut = torch.abs(self.roll) > 1.0
        pitch_cut = torch.abs(self.pitch) > 1.0

        # reference-based early termination
        cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3]
        global_root_trans = self.target_global_root_trans[:].clone()
        global_root_pos = torch.cat([global_root_trans, self.cur_ref_root_height], dim=-1)
        cur_ref_kb_pos = transform_local_to_global(self.cur_local_kb_pos, global_root_pos, self.cur_ref_root_rot)
        key_bodies_pos_diff = (cur_ref_kb_pos - cur_robot_kb_pos).pow(2).sum(-1).sqrt()  # (n_envs, n_kb)

        root_mask = self.tracking_mode == self.TrackingModes.Root.value
        telep_mask = self.tracking_mode == self.TrackingModes.Telep.value
        wholebody_mask = self.tracking_mode == self.TrackingModes.WholeBody.value
        masked_kb_pos_diff = torch.zeros_like(key_bodies_pos_diff)
        masked_kb_pos_diff[root_mask][:, self.base_kb_indices] = key_bodies_pos_diff[root_mask][:, self.base_kb_indices]
        masked_kb_pos_diff[telep_mask][:, self.telep_kb_indices] = key_bodies_pos_diff[telep_mask][:, self.telep_kb_indices]
        print("the kb dif is", masked_kb_pos_diff)
        masked_kb_pos_diff[wholebody_mask] = key_bodies_pos_diff[wholebody_mask]
        print("the kb diif us", masked_kb_pos_diff)
        masked_kb_pos_diff[wholebody_mask] = 0.0
        print("teh mask kb diff", masked_kb_pos_diff[wholebody_mask][:, self.non_terminate_kb_indices])
        
        max_key_body_pos_err = masked_kb_pos_diff.max(-1)[0]
        track_termination_dist = self.cur_termination_dist[:].clone()
        max_kb_pos_err_cut = max_key_body_pos_err > track_termination_dist
        
        self.reset_buf |= self.time_out_buf
        self.reset_buf |= (height_cutoff * self.cfg.env.enable_height_termination)
        self.reset_buf |= (roll_cut * self.cfg.env.enable_orient_termination)
        self.reset_buf |= (pitch_cut * self.cfg.env.enable_orient_termination)
        # add the ET diff curriculum
        self.reset_buf |= (max_kb_pos_err_cut * self.cfg.env.enable_max_kb_pos_err_cut)

    def update_ref_motion_obs(self):
        next_ref_motion = self.motion_lib.get_motion_state(self.next_ref_motion_ids.view(-1), self.next_ref_motion_times.view(-1))
        # all items are in the shape of (num_envs, num_future_steps, feature_dim)
        (
            next_ref_root_height,
            target_delta_proj_grav,
            target_delta_facing_vec,
            target_local_root_vel,
            target_local_root_ang_vel,
            target_dof_pos,
            target_local_kb_pos,
            target_local_kb_vel
        ) = self.extract_ref_motion_obs(next_ref_motion)
        # mask the task-irrelevant features
        # we mask the root_height command as a default constant at the root tracking mode.
        # next_ref_root_height[self.root_mask] = self.default_root_height
        # the joint and keybody infos are masked out at the root mode.
        target_dof_pos[self.root_mask] = 0.0
        target_local_kb_pos[self.root_mask] = 0.0
        target_local_kb_vel[self.root_mask] = 0.0

        # we mask the lower body info at the telep tracking mode.
        target_dof_pos[self.telep_mask][..., :self.cfg.rewards.num_lower_joints] = 0.0
        flat_target_local_kb_pos = target_local_kb_pos.view(self.num_envs, self.num_future_steps, self.num_key_bodies, 3)
        flat_target_local_kb_pos[self.telep_mask][..., self.lower_kb_indices, :] = 0.0
        flat_target_local_kb_vel = target_local_kb_vel.view(self.num_envs, self.num_future_steps, self.num_key_bodies, 3)
        flat_target_local_kb_vel[self.telep_mask][..., self.lower_kb_indices, :] = 0.0

        target_task_obs = torch.cat([
            next_ref_root_height,
            target_delta_proj_grav,
            target_delta_facing_vec,
            target_local_root_vel * self.obs_scales.lin_vel,
            target_local_root_ang_vel * self.obs_scales.ang_vel,
            target_dof_pos * self.obs_scales.dof_pos,
            flat_target_local_kb_pos.view(self.num_envs, self.num_future_steps, -1),
            target_local_kb_vel.view(self.num_envs, self.num_future_steps, -1),
        ], dim=-1).view(self.num_envs, -1)
        self.task_observation_buf[:] = target_task_obs

        # update the cur ref motion for reward and metrics calculation
        self.update_cur_ref_motion_obs()

        self.ang_z_vel_cmd[:] = target_local_root_ang_vel[:, 0, 2] * self.obs_scales.ang_vel
        # extract root command from the ref motion
        self.root_commands[:, 0:2] = target_local_root_vel[:, 0, 0:2]
        self.root_commands[:, 0] *= torch.abs(self.root_commands[..., 0]) > self.cfg.commands.lin_vel_clip
        self.root_commands[:, 1] *= torch.abs(self.root_commands[..., 1]) > self.cfg.commands.lin_vel_clip
        self.root_commands[:, 2] = target_local_root_ang_vel[:, 0, 2]
        self.root_commands[:, 2] *= torch.abs(self.root_commands[..., 2]) > self.cfg.commands.ang_vel_clip
        self.root_commands[:, 3] = next_ref_root_height.view(self.num_envs, self.num_future_steps, 1)[:, 0, 0]
    
    # def calculate_tracking_metrics(self):
    #     # calculate extra tracking metrics for policy evalaution
    #     # gmpjpe and max_gmpjpe 
    #     cur_local_kb_pos = self.cur_local_kb_pos.clone()
    #     target_global_root_pos = torch.cat([self.target_global_root_trans, self.cur_ref_root_height], dim=-1)
    #     global_root_rot = self.cur_ref_root_rot[:].clone()
    #     cur_ref_kb_pos = transform_local_to_global(cur_local_kb_pos, target_global_root_pos, global_root_rot)
    #     cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3].clone()
        
    #     # mask-out the task irrelevant tracking metrics
    #     global_kb_pos_diff = (cur_ref_kb_pos - cur_robot_kb_pos).pow(2).sum(-1).sqrt()  # (n_envs, n_kbs, )
    #     global_kb_pos_diff[self.root_mask][:, self.limbs_kb_indices] = 0
    #     global_kb_pos_diff[self.telep_mask][:, self.lower_kb_indices] = 0

    #     global_kb_pos_err = global_kb_pos_diff[global_kb_pos_diff != 0].mean(-1)
    #     max_global_kb_pos_err = global_kb_pos_diff.max(-1)[0]
    #     self.cum_tracking_errors["gmpjpe"] += global_kb_pos_err
    #     self.cum_tracking_errors["max_gmpjpe"] += max_global_kb_pos_err
    #     self.tracking_errors["gmpjpe"] = global_kb_pos_err
    #     self.tracking_errors["max_gmpjpe"] = max_global_kb_pos_err
    #     # mpjpe
    #     local_kb_pos_diff = (cur_local_kb_pos - self.robot_local_kb_pos).pow(2).sum(-1).sqrt()
    #     local_kb_pos_err = local_kb_pos_diff.mean(-1)
    #     self.cum_tracking_errors["mpjpe"] += local_kb_pos_err
    #     self.tracking_errors["mpjpe"] = local_kb_pos_err

    def calculate_state_ref_similarity(self, env_ids):
        # calculate base diff

        return
    
    # ======================================================================================================================
    # Reward functions
    # ======================================================================================================================
    # def _reward_tracking_ref_dof_pos(self):
    #     joint_pos = self.dof_pos.clone()
    #     ref_dof_pos = self.cur_ref_dof_pos.clone()
        
    #     tracking_sigma = torch.ones_like(joint_pos)
    #     # we releax the tracking precision to keep the walking/swing pattern for root and telep tracking mode
    #     tracking_sigma[self.root_mask][..., :self.cfg.rewards.num_lower_joints] *= 0.2
    #     tracking_sigma[self.root_mask][..., self.cfg.rewards.num_lower_joints:] *= 0.1
    #     tracking_sigma[self.telep_mask][..., :self.cfg.rewards.num_lower_joints] *= 0.5
    #     # we mask the ref dof to be the default_dof_pos when the root command is stance
    #     default_dof_pos_mask = torch.logical_and(~self.get_walking_cmd_mask(), self.root_mask)
    #     ref_dof_pos[default_dof_pos_mask] = self.default_dof_pos

    #     diff = (joint_pos - ref_dof_pos)
    #     r = torch.exp(-torch.norm(tracking_sigma*diff, dim=1))
    #     return r
    
    # def _reward_tracking_ref_dof_vel(self):
    #     joint_vel = self.dof_vel.clone()
    #     ref_dof_vel = self.cur_ref_dof_vel.clone()
        
    #     tracking_sigma = torch.ones_like(joint_vel) * 0.05
    #     tracking_sigma[self.root_mask][..., :self.cfg.rewards.num_lower_joints] *= 0.2
    #     tracking_sigma[self.root_mask][..., self.cfg.rewards.num_lower_joints:] *= 0.2
    #     tracking_sigma[self.telep_mask][..., :self.cfg.rewards.num_lower_joints] *= 0.5
    #     # we mask the ref dof pos to zero when the root command is stance
    #     zero_dof_vel_mask = torch.logical_and(~self.get_walking_cmd_mask(), self.root_mask)
    #     ref_dof_vel[zero_dof_vel_mask] = 0.0
        
    #     diff = joint_vel - ref_dof_vel
    #     r = torch.exp(-torch.norm(tracking_sigma * diff, dim=1))
    #     return r
    
    # def _reward_tracking_ref_kb_pos(self):
    #     cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3]
    #     global_root_trans = self.target_global_root_trans[:].clone()
    #     global_root_pos = torch.cat([global_root_trans, self.cur_ref_root_height], dim=-1)
    #     cur_ref_kb_pos = transform_local_to_global(self.cur_local_kb_pos, global_root_pos, self.cur_ref_root_rot)
    #     cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3]
    #     diff = (cur_ref_kb_pos - cur_robot_kb_pos)  # (n_envs, n_bodies, 3)
        
    #     tracking_sigma = torch.ones_like(diff) * 1.0
    #     # we mask out the limbs kb pos tracking for root_mode
    #     tracking_sigma[self.root_mask][:, self.limbs_kb_indices] *= 0
    #     # we releax the lower-body tracking for telep_mode
    #     tracking_sigma[self.telep_mask][:, self.lower_kb_indices] *= 0.5

    #     r = torch.exp(-torch.norm((tracking_sigma * diff).view(self.num_envs, -1), dim=1))
    #     return r
    
    # def _reward_tracking_ref_kb_rot(self):
    #     cur_ref_kb_rot = self.cur_ref_kb_rot.clone()
    #     cur_robot_kb_rot = self.rigid_body_states[:, self.key_body_indices, 3:7]
    #     diff = quat_diff_norm(cur_ref_kb_rot, cur_robot_kb_rot)
        
    #     tracking_sigma = torch.ones_like(diff) * 2.0
    #     tracking_sigma[self.root_mask][:, self.limbs_kb_indices] *= 0
    #     tracking_sigma[self.telep_mask][:, self.lower_kb_indices] *= 0.5
        
    #     r = torch.exp(-torch.mean(tracking_sigma * diff, dim=-1))
    #     return r
    
    # def _reward_tracking_ref_kb_lin_vel(self):
    #     cur_ref_kb_vel = self.cur_ref_kb_vel.clone()
    #     cur_robot_kb_vel = self.rigid_body_states[:, self.key_body_indices, 7:10]
    #     diff = cur_ref_kb_vel - cur_robot_kb_vel
        
    #     tracking_sigma = torch.ones_like(diff) * 0.1
    #     tracking_sigma[self.root_mask][:, self.limbs_kb_indices] *= 0
    #     tracking_sigma[self.telep_mask][:, self.lower_kb_indices] *= 0.5

    #     r = torch.exp(-torch.norm((tracking_sigma * diff).view(self.num_envs, -1), dim=1))
    #     return r
    
    # def _reward_tracking_ref_kb_ang_vel(self):
    #     cur_ref_kb_ang_vel = self.cur_ref_kb_ang_vel.clone()
    #     cur_robot_kb_ang_vel = self.rigid_body_states[:, self.key_body_indices, 10:13]
    #     diff = cur_ref_kb_ang_vel - cur_robot_kb_ang_vel

    #     tracking_sigma = torch.ones_like(diff) * 0.02
    #     tracking_sigma[self.root_mask][:, self.limbs_kb_indices] *= 0
    #     tracking_sigma[self.telep_mask][:, self.lower_kb_indices] *= 0.5

    #     r = torch.exp(-torch.norm((tracking_sigma * diff).view(self.num_envs, -1), dim=1))
    #     return r
