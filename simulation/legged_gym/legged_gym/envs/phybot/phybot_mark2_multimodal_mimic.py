import torch
import numpy as np
from .phybot_mark2_mimic import PM2Mimic, transform_local_to_global
from .phybot_mark2_mimic_sparse_config import PM2MimicSparseCfg
from legged_gym.gym_utils.helpers import class_to_dict
from legged_gym.envs.base.humanoid import Humanoid
from enum import Enum

class PM2MultimodalMimic(PM2Mimic):
    class TrackingModes(Enum):
        """
        # Tracking modes for task object assignment
        Root: trackng the root_command extracted from the ref traj
        Telep: tracking root and upper body ref
        WholeBody: tracking root, upper body and lower body ref
        """
        # the tracking mode
        Root = 0
        Telep = 1
        WholeBody = 2
    
    class InterventionCases(Enum):
        """
        # intervention case for task object adjustment
        Transition: transition from one motion clip to another follow
                the last tracking mode.
        Compliant: Switch to the force(disturibance)-adaptive mode with 
                external applied disturbance.
        """
        Transition = 0
        Compliant = 1
    
    def __init__(self, cfg: PM2MimicSparseCfg, sim_params, physics_engine, sim_device, headless):
        self.cfg = cfg
        self.global_trans_reset_time = cfg.motion_lib.global_trans_reset_time
        self.termination_dist_curriculum = cfg.env.termination_dist_curriculum
        self.use_estimator = (self.cfg.env.n_priv != 0)
        # read the new reward groups 
        Humanoid.__init__(self, cfg, sim_params, physics_engine, sim_device, headless)
        self.last_feet_z = 0.05
        self.episode_length = torch.zeros((self.num_envs), device=self.device)
        self.feet_height = torch.zeros((self.num_envs, 2), device=self.device)
        self.state_init = self.StateInit[cfg.init_state.mode]

        # tracking_mode for multi-modal whole body controller training
        # we pre-allocate the tracking mode for each env and keep it fixed during training
        self.tracking_mode_set = torch.tensor([self.TrackingModes(mode).value for mode in self.cfg.commands.tracking_modes],
                                               device=self.device)
        
        self.tracking_mode_probs = torch.tensor(self.cfg.commands.tracking_mode_probs, device=self.device)
        indices = torch.multinomial(self.tracking_mode_probs, num_samples=self.num_envs, replacement=True)
        self.tracking_mode = self.tracking_mode_set[indices]

        self._load_motion_lib(cfg.motion_lib)
        self._init_motion_buffer(cfg.motion_lib)
        self.reset_idx(torch.tensor(range(self.num_envs), device=self.device), init=True)

    def _get_body_indices(self):
        super()._get_body_indices()
        key_body_names = self.cfg.asset.key_body_names
        upper_keybody_names = self.cfg.asset.upper_keybody_names
        lower_keybody_names = self.cfg.asset.lower_keybody_names
        base_keybody_names = self.cfg.asset.base_keybody_names
        self.upper_kb_indices = torch.tensor([key_body_names.index(name) for name in upper_keybody_names],
                                             dtype=torch.long, device=self.device)
        self.lower_kb_indices = torch.tensor([key_body_names.index(name) for name in lower_keybody_names],
                                             dtype=torch.long, device=self.device)
        self.base_kb_indices = torch.tensor([key_body_names.index(name) for name in base_keybody_names],
                                             dtype=torch.long, device=self.device)
        return

    def check_termination(self):
        # base termination condition
        self.reset_buf = torch.any(torch.norm(self.contact_forces[:, self.termination_contact_indices, :], dim=-1) > 5., dim=1)
        time_out_threshold = torch.maximum(self.motion_lib.get_motion_tick_spans(self.motion_ids), 
                                           torch.ones_like(self.motion_lib.get_motion_tick_spans(self.motion_ids)) * self.cfg.env.episode_length_s)
        if self.cfg.motion_lib.cyclic_transition:
            self.time_out_buf = self.episode_length_buf * self.dt > time_out_threshold
        else:
            self.time_out_buf = self.motion_times > time_out_threshold

        # relaxed termination condition
        height_cutoff = self.root_states[:, 2] < self.cfg.rewards.termination_height
        roll_cut = torch.abs(self.roll) > 1.0
        pitch_cut = torch.abs(self.pitch) > 1.0

        # reference-based early termination
        cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3]
        global_root_trans = self.target_global_root_trans[:].clone()
        global_root_pos = torch.cat([global_root_trans, self.cur_ref_root_height], dim=-1)
        cur_ref_kb_pos = transform_local_to_global(self.cur_local_kb_pos, global_root_pos, self.cur_ref_root_rot)
        key_bodies_pos_diff = (cur_ref_kb_pos - cur_robot_kb_pos).pow(2).sum(-1).sqrt()  # (n_envs, n_kb)

        print("the tracking mode is ", self.tracking_mode)
        root_mask = self.tracking_mode == self.TrackingModes.Root.value
        telep_mask = self.tracking_mode == self.TrackingModes.Telep.value
        wholebody_mask = self.tracking_mode == self.TrackingModes.WholeBody.value
        masked_kb_pos_diff = torch.zeros_like(key_bodies_pos_diff)
        print("the root mask and ", root_mask, self.base_kb_indices)
        masked_kb_pos_diff[root_mask][:, self.base_kb_indices] = key_bodies_pos_diff[root_mask][:, self.base_kb_indices]
        masked_kb_pos_diff[telep_mask][:, self.upper_kb_indices] = key_bodies_pos_diff[telep_mask][:, self.upper_kb_indices]
        masked_kb_pos_diff[wholebody_mask][:, self.base_kb_indices] = key_bodies_pos_diff[wholebody_mask][:, self.base_kb_indices]
        
        max_key_body_pos_err = masked_kb_pos_diff.max(-1)[0]
        track_termination_dist = self.cur_termination_dist[:].clone()
        max_kb_pos_err_cut = max_key_body_pos_err > track_termination_dist
        
        self.reset_buf |= self.time_out_buf
        self.reset_buf |= (height_cutoff * self.cfg.env.enable_height_termination)
        self.reset_buf |= (roll_cut * self.cfg.env.enable_orient_termination)
        self.reset_buf |= (pitch_cut * self.cfg.env.enable_orient_termination)
        # add the ET diff curriculum
        self.reset_buf |= (max_kb_pos_err_cut * self.cfg.env.enable_max_kb_pos_err_cut)

    # def update_ref_motion_obs(self):
    #     next_ref_motion = self.motion_lib.get_motion_state(self.next_ref_motion_ids.view(-1), self.next_ref_motion_times.view(-1))
    #     (
    #         next_ref_root_height,
    #         target_delta_proj_grav,
    #         target_delta_facing_vec,
    #         target_local_root_vel,
    #         target_local_root_ang_vel,
    #         target_dof_pos,
    #         target_local_kb_pos,
    #         target_local_kb_vel
    #     ) = self.extract_ref_motion_obs(next_ref_motion)


    def calculate_state_ref_similarity(self, env_ids):
        # calculate base diff

        return