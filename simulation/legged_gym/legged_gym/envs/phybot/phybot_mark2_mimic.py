from legged_gym import LEGGED_GYM_ROOT_DIR, envs
from time import time
from warnings import WarningMessage
import numpy as np
from isaacgym.torch_utils import *
from isaacgym import gymtorch, gymapi, gymutil
import torch
from legged_gym import LEGGED_GYM_ROOT_DIR, POSE_DIR
from legged_gym.envs.base.base_task import BaseTask
from legged_gym.envs.base.humanoid import Humanoid
from .phybot_mark2_mimic_config import PM2MimicCfg
from legged_gym.envs.base.legged_robot import euler_from_quaternion
from legged_gym.gym_utils.math import *
from legged_gym.utils.motion_lib_base import MotionLib
from legged_gym.utils.torch_utils import calc_heading_quat, quat_diff_norm, calc_heading_quat_inv
from enum import Enum
import math


class PM2Mimic(Humanoid):
    class StateInit(Enum):
        Default = 0
        Mocap = 1
        Fall = 2
        Hybrid = -1

    def __init__(self, cfg: PM2MimicCfg, sim_params, physics_engine, sim_device, headless):
        self.cfg = cfg
        self.global_trans_reset_time = cfg.motion_lib.global_trans_reset_time
        self.termination_dist_curriculum = cfg.env.termination_dist_curriculum
        self.use_estimator = (self.cfg.env.n_priv != 0)
        super().__init__(cfg, sim_params, physics_engine, sim_device, headless)
        self.last_feet_z = 0.05
        self.episode_length = torch.zeros((self.num_envs), device=self.device)
        self.feet_height = torch.zeros((self.num_envs, 2), device=self.device)
        self.state_init = self.StateInit[cfg.init_state.mode]
        self._load_motion_lib(cfg.motion_lib)
        self._init_motion_buffer(cfg.motion_lib)
        self.reset_idx(torch.tensor(range(self.num_envs), device=self.device), init=True)
    
    def _get_body_indices(self):
        upper_arm_names = [s for s in self.body_names if self.cfg.asset.upper_arm_name in s]
        lower_arm_names = [s for s in self.body_names if self.cfg.asset.lower_arm_name in s]
        torso_name = [s for s in self.body_names if self.cfg.asset.torso_name in s]
        key_body_names = self.cfg.asset.key_body_names
        self.num_key_bodies = len(key_body_names)
        non_terminate_body_names = self.cfg.asset.non_terminate_body_names

        self.non_terminate_kb_indices = torch.tensor([key_body_names.index(name) for name in non_terminate_body_names],
                                             dtype=torch.long, device=self.device)

        self.torso_indices = torch.zeros(len(torso_name), dtype=torch.long, device=self.device,
                                                 requires_grad=False)
        self.key_body_indices = torch.zeros(len(key_body_names), dtype=torch.long, device=self.device, requires_grad=False)
        for k in range(len(key_body_names)):
            self.key_body_indices[k] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                             key_body_names[k])
        for j in range(len(torso_name)):
            self.torso_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                  torso_name[j])
        self.upper_arm_indices = torch.zeros(len(upper_arm_names), dtype=torch.long, device=self.device,
                                                     requires_grad=False)
        for j in range(len(upper_arm_names)):
            self.upper_arm_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                upper_arm_names[j])
        self.lower_arm_indices = torch.zeros(len(lower_arm_names), dtype=torch.long, device=self.device,
                                                requires_grad=False)
        for j in range(len(lower_arm_names)):
            self.lower_arm_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                lower_arm_names[j])
        knee_names = [s for s in self.body_names if self.cfg.asset.shank_name in s]
        self.knee_indices = torch.zeros(len(knee_names), dtype=torch.long, device=self.device, requires_grad=False)
        for i in range(len(knee_names)):
            self.knee_indices[i] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0], knee_names[i])

        waist_names = [s for s in self.body_names if self.cfg.asset.waist_name in s]
        self.waist_indices = torch.zeros(len(waist_names), dtype=torch.long, device=self.device, requires_grad=False)
        for i in range(len(waist_names)):
            self.waist_indices[i] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0], waist_names[i])

        # get the ref motion indices
        ref_rigid_body_names = self.cfg.motion_lib.rigid_body_names
        self.ref_body_indices = torch.tensor([ref_rigid_body_names.index(name) for name in self.body_names],
                                             dtype=torch.long, device=self.device)
        self.ref_torso_indices = self.ref_body_indices[self.torso_indices]
        self.ref_upper_arm_indices = self.ref_body_indices[self.upper_arm_indices]
        self.ref_lower_arm_indices = self.ref_body_indices[self.lower_arm_indices]
        self.ref_knee_indices = self.ref_body_indices[self.knee_indices]
        self.ref_dof_indices = self.ref_body_indices[1:] - 1  # exclude the root body

        self.ref_kb_indices = self.ref_body_indices[self.key_body_indices]

        # get euler order of the root joint groups
        self.euler_orders = []
        euler_mapping = {"roll": "X", "pitch": "Y", "yaw": "Z", "knee": "Y", "toe": "Y"}
        for group_name in self.cfg.asset.whole_body_groups:
            euler_order = [euler_mapping[name.split("_")[-1]] for name in self.dof_names if group_name in name]
            self.euler_orders.append(euler_order)

    def _load_motion_lib(self, motion_cfg):
        motion_dir = motion_cfg.motion_base.format(LEGGED_GYM_ROOT_DIR=LEGGED_GYM_ROOT_DIR)
        if motion_cfg.pre_processed:
            self.motion_lib = MotionLib(
                motion_dir,
                device=motion_cfg.device,
                motion_folder=motion_cfg.motion_dump,
                pre_processed=True,
                feet_idx=motion_cfg.feet_idx,
            )
        else:
            self.motion_lib = MotionLib(
                motion_dir,
                robot_name=motion_cfg.robot_name,
                device=motion_cfg.device,
                pre_processed=False,
                num_to_load=motion_cfg.num_to_load,
                specified_ids=motion_cfg.specified_ids,
                feet_idx=motion_cfg.feet_idx,
            )
        # allocate the ref motion to each env by uniform
        self.num_future_steps = self.cfg.env.num_future_steps
        self.num_interval_steps = self.cfg.env.num_interval_steps
        self.num_motions = self.motion_lib.num_motions

    def _init_motion_buffer(self, motion_cfg):
        motion_ids = torch.arange(self.num_envs, dtype=torch.long, device=self.device)
        self.truncate_phase = motion_cfg.truncate_phase
        # allocate the motion tracks uniformly.
        self.motion_ids = torch.fmod(motion_ids, self.num_motions).to(self.device)
        self.motion_times = self.motion_lib.sample_time(self.motion_ids, truncate_phase=self.truncate_phase)
        self.next_ref_motion_ids = self.motion_ids.unsqueeze(-1).tile((1, self.num_future_steps))
        self.next_ref_motion_times = torch.zeros_like(self.next_ref_motion_ids, dtype=torch.float32, device=self.device)
        self.reset_motion_times = self.motion_times.clone()
        self.remained_motion_times = 100*torch.ones(self.num_envs, dtype=torch.float32, device=self.device)

        self.target_global_root_trans = torch.zeros((self.num_envs, 2), dtype=torch.float32, device=self.device)
        self.cur_local_kb_pos = torch.zeros((self.num_envs, len(self.key_body_indices), 3), dtype=torch.float32, device=self.device)
        self.cur_ref_kb_rot = torch.zeros((self.num_envs, len(self.key_body_indices), 4), dtype=torch.float32, device=self.device)
        self.cur_ref_kb_vel = torch.zeros((self.num_envs, len(self.key_body_indices), 3), dtype=torch.float32, device=self.device)
        self.cur_ref_kb_ang_vel = torch.zeros((self.num_envs, len(self.key_body_indices), 3), dtype=torch.float32, device=self.device)
        self.cur_ref_root_rot = torch.zeros((self.num_envs, 4), dtype=torch.float32, device=self.device)
        self.cur_ref_root_height = torch.zeros((self.num_envs, 1), dtype=torch.float32, device=self.device)
        self.cur_ref_root_pos = torch.zeros((self.num_envs, 3), dtype=torch.float32, device=self.device)
        self.cur_ref_root_vel = torch.zeros((self.num_envs, 3), dtype=torch.float32, device=self.device)
        self.cur_ref_root_ang_vel = torch.zeros((self.num_envs, 3), dtype=torch.float32, device=self.device)
        self.cur_ref_dof_pos = torch.zeros((self.num_envs, self.num_dof), dtype=torch.float32, device=self.device)
        self.cur_ref_dof_vel = torch.zeros((self.num_envs, self.num_dof), dtype=torch.float32, device=self.device)
        self.init_ref_root_offset = torch.zeros((self.num_envs, 2), dtype=torch.float32, device=self.device)
        self.cur_ref_contact_profile = torch.zeros((self.num_envs, len(motion_cfg.feet_idx)), dtype=torch.bool, device=self.device)

        # the next ref motion as task observation
        self.task_observation_buf = torch.zeros((self.num_envs, self.cfg.env.num_task_observations), device=self.device)
        self.root_commands = torch.zeros((self.num_envs, 4), device=self.device)
        self.ang_z_vel_cmd = torch.zeros((self.num_envs, ), device=self.device)
        _, _, self.target_yaw = euler_from_quaternion(self.cur_ref_root_rot)
        self.robot_local_kb_pos = torch.zeros((self.num_envs, len(self.key_body_indices), 3), device=self.device)

        self.base_termination_dist = self.cfg.env.init_termination_dist * torch.ones((self.num_envs, ), device=self.device)
        self.cur_termination_dist = self.cfg.env.init_termination_dist * torch.ones((self.num_envs, ), device=self.device)
        self.motion_completion_rate = torch.zeros((self.num_motions, ), device=self.device)

        # tracking metrics for evaluation
        self.tracking_metrics = self.cfg.rewards.tracking_metrics
        self.tracking_errors = {name: torch.zeros(self.num_envs, dtype=torch.float, device=self.device, requires_grad=False)
                             for name in self.tracking_metrics}
        self.cum_tracking_errors = {name: torch.zeros(self.num_envs, dtype=torch.float, device=self.device, requires_grad=False)
                             for name in self.tracking_metrics}

        if self.cfg.motion_lib.sample_scheme == "dynamic":
            self._setup_dynamic_sampling()

    def _setup_dynamic_sampling(self, start_time=0):
        # we should pre-allocate the motion start time when using the past motion as the condition of
        # diffusion planner.
        num_buckets_per_motion = []  # len == num motions
        bucket_to_motion_mapping = []  # len == total num buckets
        bucket_start_times = []
        bucket_time_spans = []
        
        # allocate the buckets for each motion clip
        bucket_width = self.cfg.motion_lib.bucket_width # in seconds
        motion_time_span = self.motion_lib.m_tick_span
        for motion_id, end_time in enumerate(motion_time_span):
            start_time = start_time
            num_buckets = math.ceil((end_time - start_time) / bucket_width)
            num_buckets_per_motion.append(num_buckets)
            for j in range(num_buckets):
                bucket_to_motion_mapping.append(motion_id)
                start = start_time + j * bucket_width
                bucket_start_times.append(start)
                end = min(start + bucket_width, end_time)
                assert end - start > 0, f"the bucket length is {end - start}!"
                bucket_time_spans.append(end-start)

        # (num_motions, 1)
        num_buckets = torch.tensor(num_buckets_per_motion, device=self.device, dtype=torch.long)
        total_num_buckets = num_buckets.sum()
        rolled = num_buckets.roll(1)
        rolled[0] = 0
        self.bucket_offsets_per_motion = rolled.cumsum(0)
        # (num_buckets, 1), init the bucket sampling weights and scores.
        self.bucket_visited_counts = torch.zeros(total_num_buckets, device=self.device, dtype=torch.long)
        self.bucket_weights = torch.zeros(total_num_buckets, device=self.device, dtype=torch.float)
        self.min_bucket_weight = (torch.ones(total_num_buckets, device=self.device, dtype=torch.float) * 
                                  self.cfg.motion_lib.min_bucket_weight)
        self.max_bucket_weight = (torch.ones(total_num_buckets, device=self.device, dtype=torch.float) * 
                                  self.cfg.motion_lib.max_bucket_weight)
        self.bucket_scores = torch.zeros(total_num_buckets, device=self.device, dtype=torch.float)
        
        self.bucket_to_motion_mapping = torch.tensor(bucket_to_motion_mapping, device=self.device, dtype=torch.long)
        self.bucket_start_times = torch.tensor(bucket_start_times, device=self.device, dtype=torch.float)
        self.bucket_time_spans = torch.tensor(bucket_time_spans, device=self.device, dtype=torch.float)

        # refresh the bucket weights every 10 epochs
        total_num_frames = self.motion_lib.m_tick_span.sum() / self.dt
        self.weight_refresh_interval = math.ceil(self.cfg.motion_lib.weight_refresh_epoch * total_num_frames / self.num_envs)

    def update_dynamic_stats(self, env_ids):
        """
        Update the motion bucket_scores after every interactive step when using
        the mimic early termination criterion, or after each episode step when using
        the mimic tracking criterion.
        """
        reset_motion_ids = self.motion_ids[env_ids]
        reset_motion_times = self.reset_motion_times[env_ids]
        # index the buckets for the failed motions
        base_offsets = self.bucket_offsets_per_motion[reset_motion_ids]
        extra_bucket_offsets = torch.floor(reset_motion_times / self.cfg.motion_lib.bucket_width).long()
        bucket_indices = base_offsets + extra_bucket_offsets

        # update the bucket visited counts and the bucket scores
        self.bucket_visited_counts.scatter_add_(0, bucket_indices, torch.ones_like(bucket_indices))
        clip_scores = torch.zeros(len(env_ids), dtype=torch.float, device=self.device)
        for key in self.episode_sums.keys():
            clip_scores += self.episode_sums[key][env_ids] * self.reward_scales[key] / self.episode_length_buf[env_ids]
        self.bucket_scores.scatter_add_(0, bucket_indices, clip_scores.clamp(min=0))

    def refresh_dynamic_weights(self):
        """
        refresh the bucket weights based on the aggregated bucket scores and visited counts.
        This operation is performed after specified epoch_interval.
        """
        visited = self.bucket_visited_counts > 0
        average_score = self.bucket_scores[visited] / self.bucket_visited_counts[visited]
        weights = torch.exp(-4.0 * average_score)
        weights /= weights.sum()

        self.bucket_weights[visited] = torch.clamp((0.3 * weights + self.bucket_weights[visited] * 0.7),
            min=self.min_bucket_weight[visited])

        # log the bucket stats
        bucket_log_dict = {
            # count in every refresh interval
            "bucket_avg_score": average_score,
            "bucket_visited_counts": self.bucket_visited_counts.float(),
            "bucket_score": self.bucket_scores,
            # count in the entire training process
            "bucket_weights": self.bucket_weights,
        }
        self.extras["dynamic_sampling"] = {}
        for k, v in bucket_log_dict.items():
            if v.shape[0] > 0:
                self.extras["dynamic_sampling"][f"{k}_min"] = v.min()
                self.extras["dynamic_sampling"][f"{k}_max"] = v.max()
                self.extras["dynamic_sampling"][f"{k}_mean"] = v.mean()

        # refresh the bucket stats
        self.bucket_visited_counts.zero_()
        self.bucket_scores.zero_()

    def next_ref_motion_indices(self, env_ids, num_future_steps=1, num_interval_steps=1):
        time_offsets = self.dt * (1 + torch.arange(num_future_steps, device=self.device) * num_interval_steps)
        future_times = time_offsets.unsqueeze(0) + self.motion_times.unsqueeze(-1)  # (envs, fut_motion_steps)
        motion_ids = self.motion_ids.unsqueeze(-1).tile((1, num_future_steps))
        
        flat_ids = motion_ids[env_ids].view(-1)
        motion_time_spans = self.motion_lib.get_motion_tick_spans(flat_ids)
        flat_times = torch.fmod(future_times[env_ids].view(-1), motion_time_spans)
        
        self.next_ref_motion_ids[env_ids] = motion_ids[env_ids]
        self.next_ref_motion_times[env_ids] = flat_times.view(env_ids.shape[0], num_future_steps)
    
    def dynamic_sample(self, n_motions: int):
        weights = self.bucket_weights.clone().clamp(min=self.min_bucket_weight)
        if (self.bucket_weights == 0).any():
            weights[self.bucket_weights == 0] = 1.0
            weights[self.bucket_weights != 0] = 0.0
            norm_weights = weights
        else:
            min_weights = weights.min()
            norm_weights = weights / min_weights
            norm_weights = norm_weights.clamp(max=self.max_bucket_weight)
        sampled_bucket_indices = torch.multinomial(norm_weights, n_motions, replacement=True)
        sampled_motion_ids = self.bucket_to_motion_mapping[sampled_bucket_indices]
        bucket_start_times = self.bucket_start_times[sampled_bucket_indices]
        bucket_time_spans = self.bucket_time_spans[sampled_bucket_indices]
        # uniform sampling inside the bucket
        sampled_motion_times = torch.rand(n_motions, device=self.device) * bucket_time_spans + bucket_start_times
        return sampled_motion_ids, sampled_motion_times

    # invoke this every reset check
    def update_ref_motion_indices(self, env_ids):
        sample_scheme = self.cfg.motion_lib.sample_scheme
        if sample_scheme == "uniform":
            motion_ids = self.motion_ids[env_ids]
            self.motion_times[env_ids] = self.motion_lib.sample_time(motion_ids, truncate_phase=self.truncate_phase)
        elif sample_scheme == "preceding":
            motion_ids = self.motion_ids[env_ids]
            self.motion_times[env_ids] = self.motion_times[env_ids] + self.dt
            self.motion_times = torch.fmod(self.motion_times, self.motion_lib.get_motion_tick_spans(self.motion_ids))
        elif sample_scheme == "dynamic":
            motion_ids, motion_times = self.dynamic_sample(len(env_ids))
            self.motion_ids[env_ids] = motion_ids
            self.motion_times[env_ids] = motion_times
            self.motion_times = torch.fmod(self.motion_times, self.motion_lib.get_motion_tick_spans(self.motion_ids))
        else:
            raise ValueError("Unknown motion sample scheme: {}".format(sample_scheme))

        if self.cfg.motion_lib.start_init_prob > 0:
            assert sample_scheme in ["uniform", "preceding"], "start_init_prob is only considered when using uniform and preceding sampling scheme"
            start_init_prob = torch.tensor(self.cfg.motion_lib.start_init_prob, dtype=torch.float32,
                                             device=self.device).repeat(len(env_ids))
            init_start_mask = torch.bernoulli(start_init_prob).bool()
            self.motion_times[env_ids[init_start_mask]] = 0

        self.remained_motion_times[env_ids] = self.motion_lib.get_motion_tick_spans(motion_ids) - self.motion_times[env_ids]

    def reset_idx(self, env_ids, init=False):
        if len(env_ids) == 0:
            return
        
        # update curriculum
        if self.cfg.terrain.curriculum:
            self._update_terrain_curriculum(env_ids)

        if self.termination_dist_curriculum and not init:
            track_completion_rate = (self.episode_length_buf[env_ids] * self.dt / 
                                     self.motion_lib.get_motion_tick_spans(self.motion_ids[env_ids]))
            track_completion_rate = torch.clamp(track_completion_rate, max=1.0)
            motion_indices = [(index, torch.where(self.motion_ids[env_ids] == index, True, False)) for index in range(self.num_motions)]
            for index, motion_id in motion_indices:
                if motion_id.any():
                    cur_completion_rate = torch.mean(track_completion_rate[motion_id])
                    self.motion_completion_rate[index] += 0.5 * (cur_completion_rate - self.motion_completion_rate[index])
            self.cur_termination_dist[env_ids] = (self.base_termination_dist[env_ids] +
                                                  self.motion_completion_rate[self.motion_ids[env_ids]] * self.cfg.env.dist_curriculum_scale)
        
        # update the dynamic motion sampling stats
        if not init and self.cfg.motion_lib.sample_scheme == "dynamic":
            self.update_dynamic_stats(env_ids)
        # reset robot states
        self.update_ref_motion_indices(env_ids)
        ref_motion_ids, ref_motion_times = self.motion_ids[env_ids], self.motion_times[env_ids]
        # record the reset motion times after reset
        self.reset_motion_times[env_ids] = ref_motion_times.clone()
        ref_motion = self.motion_lib.get_motion_state(ref_motion_ids, ref_motion_times)
        if self.state_init == self.StateInit.Default:
            dof_pos = self.default_dof_pos_all.clone()[env_ids]
            dof_pos *= torch_rand_float(0.8, 1.2, (len(env_ids), self.num_dof), device=self.device)
            dof_vel = torch.zeros_like(dof_pos)
            root_vel, root_quat, root_height = None, None, None
        
        elif self.state_init == self.StateInit.Mocap:
            dof_pos = ref_motion.dof_pos[..., self.ref_dof_indices].clone()
            dof_vel = ref_motion.dof_vel[..., self.ref_dof_indices].clone()
            root_vel = ref_motion.rb_vel[:, 0].clone()
            root_quat =  ref_motion.rb_rot[:, 0].clone().squeeze(0)
            root_height = ref_motion.rb_pos[:, 0, 2].clone()
            if self.cfg.motion_lib.init_default_prob > 0:
                default_state_prob = torch.tensor(self.cfg.motion_lib.init_default_prob, dtype=torch.float32,
                                             device=self.device).repeat(len(env_ids))
                default_state_mask = torch.bernoulli(default_state_prob).bool()
                dof_pos[default_state_mask] = self.default_dof_pos_all[env_ids[default_state_mask]]
                dof_vel[default_state_mask] = torch.zeros_like(dof_pos[default_state_mask])
                root_vel[default_state_mask] = torch.zeros_like(root_vel[default_state_mask])
                root_height[default_state_mask] = torch.ones_like(root_height[default_state_mask]) * self.default_root_height
        
        elif self.state_init == self.StateInit.Fall:
            dof_pos = -1
            dof_vel = -1
            root_vel, root_quat, root_height = 0, 0, 0  # to add further
        else:
            raise NotImplementedError

        self._reset_dofs(env_ids, dof_pos, dof_vel)
        self._reset_root_states(env_ids, root_vel, root_quat, root_height)

        # account for the odometer difference between the ref motion and the robot
        cur_global_root_pos = self.root_states[env_ids, :3].clone()
        self.target_global_root_trans[env_ids] = cur_global_root_pos[:, :2]
        self.init_ref_root_offset[env_ids] = cur_global_root_pos[:, :2] - ref_motion.rb_pos[:, 0, :2]

        if self.cfg.commands.resample:
            self._resample_commands(env_ids)  # no resample commands

        self.gym.simulate(self.sim)
        self.gym.fetch_results(self.sim, True)
        self.gym.refresh_rigid_body_state_tensor(self.sim)

        # reset buffers
        self.last_actions[env_ids] = 0.
        self.last_dof_vel[env_ids] = 0.
        self.last_torques[env_ids] = 0.
        self.last_root_vel[:] = 0.
        self.feet_air_time[env_ids] = 0.
        self.reset_buf[env_ids] = 1
        self.obs_history_buf[env_ids, :, :] = 0.
        self.contact_buf[env_ids, :, :] = 0.
        self.action_history_buf[env_ids, :, :] = 0.
        self.feet_land_time[env_ids] = 0.
        self._reset_buffers_extra(env_ids)

        self.cur_ref_dof_pos[env_ids] = dof_pos
        self.cur_ref_dof_vel[env_ids] = dof_vel
        self.cur_ref_root_pos[env_ids] = cur_global_root_pos
        self.cur_ref_root_height[env_ids] = cur_global_root_pos[:, 2:3]
        self.cur_ref_root_rot[env_ids] = self.root_states[env_ids, 3:7].clone()
        self.cur_ref_root_vel[env_ids] = self.root_states[env_ids, 7:10].clone()
        self.cur_ref_root_ang_vel[env_ids] = self.root_states[env_ids, 10:13].clone()
        cur_ref_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3].clone()
        self.cur_local_kb_pos[env_ids] = transform_global_to_local(cur_ref_kb_pos[env_ids], cur_global_root_pos,
                                                                   self.cur_ref_root_rot[env_ids])
        self.cur_ref_kb_rot[env_ids] = self.rigid_body_states[:, self.key_body_indices, 3:7].clone()[env_ids]
        self.robot_local_kb_pos[env_ids] = transform_global_to_local(cur_ref_kb_pos[env_ids],
                                                                     self.root_states[env_ids, :3], 
                                                                     self.base_quat[env_ids])
        self.cur_ref_kb_vel[env_ids] = self.rigid_body_states[:, self.key_body_indices, 7:10].clone()[env_ids]
        self.cur_ref_kb_ang_vel[env_ids] = self.rigid_body_states[:, self.key_body_indices, 10:13].clone()[env_ids]

        self.cur_ref_contact_profile[env_ids] = ref_motion.feet_contact_profile.clone()
        
        # fill extras
        self.extras["episode"] = {}
        for key in self.episode_sums.keys():
            self.extras["episode"]['metric_' + key] = torch.mean(
                self.episode_sums[key][env_ids] / self.episode_length_buf[env_ids])
            self.extras["episode"]['rew_' + key] = torch.mean(
                self.episode_sums[key][env_ids] * self.reward_scales[key] / self.episode_length_buf[env_ids])
            self.episode_sums[key][env_ids] = 0.
        # log the extra tracking metrics
        self.extras["tracking"] = {}
        for key in self.tracking_metrics:
            self.extras["tracking"][key] = torch.mean(self.cum_tracking_errors[key][env_ids] / self.episode_length_buf[env_ids])
            self.cum_tracking_errors[key][env_ids] = 0.
        # mean over the entire tracks
        self.extras["tracking"]["cur_termination_dist"] = torch.mean(self.cur_termination_dist[:])
        
        self.episode_length_buf[env_ids] = 0

        # log additional curriculum info
        if self.cfg.terrain.curriculum:
            self.extras["episode"]["terrain_level"] = torch.mean(self.terrain_levels.float())
        if self.cfg.commands.curriculum:
            self.extras["episode"]["max_command_x"] = self.command_ranges["lin_vel_x"][1]
        # send timeout info to the algorithm
        if self.cfg.env.send_timeouts:
            self.extras["time_outs"] = self.time_out_buf
        return

    def _reset_dofs(self, env_ids, dof_pos, dof_vel):
        self.dof_pos[env_ids] = dof_pos.squeeze(0)
        self.dof_vel[env_ids] = dof_vel.squeeze(0)

        env_ids_int32 = env_ids.to(dtype=torch.int32)
        self.gym.set_dof_state_tensor_indexed(self.sim,
                                              gymtorch.unwrap_tensor(self.dof_state),
                                              gymtorch.unwrap_tensor(env_ids_int32), len(env_ids_int32))

    def post_physics_step(self):
        super().post_physics_step()
        # step motion lib
        self.motion_times += self.dt
        self.motion_times = torch.fmod(self.motion_times, self.motion_lib.get_motion_tick_spans(self.motion_ids))
        self._update_goals()
        env_ids = np.arange(self.num_envs)
        self.next_ref_motion_indices(env_ids, self.num_future_steps, self.num_interval_steps)
        self.update_ref_motion_obs()

        if self.cfg.motion_lib.sample_scheme == "dynamic":
            if self.global_counter % self.weight_refresh_interval == 0:
                self.refresh_dynamic_weights()

        if self.viewer and self.enable_viewer_sync and self.debug_viz:
            if self.cfg.motion_lib.motion_sync:
                self._motion_sync()
                self.gym.refresh_actor_root_state_tensor(self.sim)
                self.gym.refresh_net_contact_force_tensor(self.sim)
                self.gym.refresh_rigid_body_state_tensor(self.sim)
                self.gym.refresh_force_sensor_tensor(self.sim)
            self.gym.clear_lines(self.viewer)
            self.draw_key_bodies_demo()

        return

    # update the task goal extractd from the ref motion
    def _update_goals(self):
        reset_mask = self.episode_length_buf % (self.global_trans_reset_time // self.dt) == 0
        self.target_global_root_trans[reset_mask] = self.root_states[reset_mask, :2]
        # update the global trans odometer according to the ref motion
        self.target_global_root_trans += self.cur_ref_root_vel[:, :2] * self.dt
        _, _, self.target_yaw = euler_from_quaternion(self.cur_ref_root_rot)

    def _get_noise_scale_vec(self, cfg):
        noise_vec = torch.zeros(self.cfg.env.n_proprio).to(self.device)
        self.add_noise = self.cfg.noise.add_noise
        noise_scales = self.cfg.noise.noise_scales
        noise_level = self.cfg.noise.noise_level
        # the first 4 items are the root commands.
        noise_vec[4:7] = noise_scales.ang_vel * noise_level * self.obs_scales.ang_vel
        noise_vec[7:10] = noise_scales.gravity * noise_level
        noise_vec[10:10+self.num_dof] = noise_scales.dof_pos * noise_level * self.obs_scales.dof_pos
        noise_vec[10+self.num_dof:10+2*self.num_dof] = noise_scales.dof_vel * noise_level * self.obs_scales.dof_vel
        return noise_vec

    def compute_state_observation(self):
        proj_grav = quat_rotate_inverse(self.base_quat, self.gravity_vec)
        if self.cfg.commands.resample:
            root_command = self.commands
        else:
            root_command = self.root_commands
        
        cur_ref_dof_pos = self.cur_ref_dof_pos.clone() if self.cfg.control.control_type == "residual_pos"\
              else self.default_dof_pos_all

        return torch.cat((
            root_command,  # 4
            self.base_ang_vel * self.obs_scales.ang_vel,  # 3
            proj_grav, # 3
            (self.dof_pos - cur_ref_dof_pos) * self.obs_scales.dof_pos,
            self.dof_vel * self.obs_scales.dof_vel,
            self.action_history_buf[:, -1]), dim=-1)

    def compute_observations(self):
        state_buf = self.compute_state_observation()
        # noise schedule when training
        if self.cfg.noise.add_noise and self.headless:
            state_buf += (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec * min(
                self.total_env_steps_counter / (self.cfg.noise.noise_increasing_steps * 24), 1.)
        elif self.cfg.noise.add_noise and not self.headless:
            state_buf += (2 * torch.rand_like(state_buf) - 1) * self.noise_scale_vec
        else:
            state_buf += 0.

        if self.cfg.domain_rand.domain_rand_general and self.cfg.env.n_priv_latent > 0:
            priv_latent = torch.cat((
                self.mass_params_tensor,
                self.env_frictions,
                self.motor_strength[0] - 1,
                self.motor_strength[1] - 1,
            ), dim=-1)
        else:
            priv_latent = torch.zeros((self.num_envs, self.cfg.env.n_priv_latent), device=self.device)
        
        if self.cfg.env.n_priv > 0:
            feet_contact = self.contact_filt.float() - 0.5
            priv_explicit = torch.cat((
                self.root_states[:, 2:3],
                feet_contact,
                self.base_lin_vel * self.obs_scales.lin_vel,
                self.robot_local_kb_pos.view(self.num_envs, -1),
            ), dim=-1)
        else:
            priv_explicit = torch.zeros((self.num_envs, self.cfg.env.n_priv), device=self.device)

        obs_task = self.task_observation_buf.view(self.num_envs, -1)

        self.obs_buf = torch.cat([state_buf, obs_task, priv_explicit, priv_latent, self.obs_history_buf.view(self.num_envs, -1)], dim=-1)

        if self.cfg.env.history_len > 0:
            self.obs_history_buf = torch.where(
                (self.episode_length_buf <= 1)[:, None, None],
                torch.stack([state_buf] * self.cfg.env.history_len, dim=1),
                torch.cat([
                    self.obs_history_buf[:, 1:],
                    state_buf.unsqueeze(1)
                ], dim=1)
            )
        self.contact_buf = torch.where(
            (self.episode_length_buf <= 1)[:, None, None],
            torch.stack([self.contact_filt.float()] * self.cfg.env.contact_buf_len, dim=1),  # replicative initialization.
            torch.cat([
                self.contact_buf[:, 1:],
                self.contact_filt.float().unsqueeze(1)
            ], dim=1)
        )
    
    def update_cur_ref_motion_obs(self):
        ## ref motion at current env step for tracking reward/metric
        cur_ref_motion = self.motion_lib.get_motion_state(self.motion_ids, self.motion_times)
        cur_ref_root_pos = cur_ref_motion.rb_pos[:, 0].clone()
        cur_ref_root_vel = cur_ref_motion.rb_vel[:, 0].clone()
        cur_ref_root_rot = cur_ref_motion.rb_rot[:, 0].clone()
        cur_ref_kb_pos = cur_ref_motion.rb_pos[:, self.ref_kb_indices].clone()
        cur_ref_dof_pos = cur_ref_motion.dof_pos[:, self.ref_dof_indices].clone()
        self.cur_ref_root_height[:] = cur_ref_root_pos[:, 2:3]
        self.cur_ref_root_pos[:] = cur_ref_root_pos
        self.cur_ref_root_vel[:] = cur_ref_root_vel
        self.cur_ref_root_ang_vel[:] = cur_ref_motion.rb_ang_vel[:, 0].clone()
        self.cur_ref_root_rot[:] = cur_ref_root_rot
        self.cur_ref_dof_pos[:] = cur_ref_dof_pos
        self.cur_ref_dof_vel[:] = cur_ref_motion.dof_vel[:, self.ref_dof_indices].clone()
        self.cur_local_kb_pos[:] = transform_global_to_local(cur_ref_kb_pos, cur_ref_root_pos, cur_ref_root_rot)
        self.cur_ref_kb_rot[:] = cur_ref_motion.rb_rot[:, self.ref_kb_indices].clone()
        self.cur_ref_kb_vel[:] = cur_ref_motion.rb_vel[:, self.ref_kb_indices].clone()
        self.cur_ref_kb_ang_vel[:] = cur_ref_motion.rb_ang_vel[:, self.ref_kb_indices].clone()
        self.cur_ref_contact_profile[:] = cur_ref_motion.feet_contact_profile.clone()
        self.robot_local_kb_pos[:] = transform_global_to_local(self.rigid_body_states[:, self.key_body_indices, :3],
                                                                self.root_states[:, :3], self.base_quat)

    def extract_ref_motion_obs(self, next_ref_motion):
        # extract the cmd/task obs from the ref motion
        next_ref_root_rot = next_ref_motion.rb_rot[:, 0].clone()
        next_ref_root_vel = next_ref_motion.rb_vel[:, 0].clone()
        next_ref_root_ang_vel = next_ref_motion.rb_ang_vel[:, 0].clone()
        next_ref_kb_pos = next_ref_motion.rb_pos[:, self.ref_kb_indices].clone()
        next_ref_kb_vel = next_ref_motion.rb_vel[:, self.ref_kb_indices].clone()
        # extracted root command
        next_ref_root_height = next_ref_motion.rb_pos[:, 0, 2:3].clone()  # (n_env*n_steps, 1)
        next_root_proj_grav = quat_rotate_inverse(next_ref_root_rot, self.gravity_vec.repeat(self.num_future_steps, 1)) # (n_env*n_steps, 3)
        next_root_facing_vec = quat_rotate(next_ref_root_rot, self.forward_vec.repeat(self.num_future_steps, 1))
        # extract dof command
        next_ref_dof_pos = next_ref_motion.dof_pos[:, self.ref_dof_indices].clone()

        ## extract the fut task targets w.r.t the robot root or the ref root.
        rolled_ref_root_rot = next_ref_root_rot.reshape(self.num_envs, self.num_future_steps, 4).roll(shifts=1, dims=1)
        rolled_ref_root_vel = next_ref_root_vel.reshape(self.num_envs, self.num_future_steps, 3).roll(shifts=1, dims=1)
        rolled_ref_root_ang_vel = next_ref_root_ang_vel.reshape(self.num_envs, self.num_future_steps, 3).roll(shifts=1, dims=1)
        rolled_ref_kb_pos = next_ref_kb_pos.reshape(self.num_envs, self.num_future_steps, len(self.ref_kb_indices), 3).roll(shifts=1, dims=1)
        rolled_ref_kb_vel = next_ref_kb_vel.reshape(self.num_envs, self.num_future_steps, len(self.ref_kb_indices), 3).roll(shifts=1, dims=1)
        rolled_ref_dof_pos = next_ref_dof_pos.reshape(self.num_envs, self.num_future_steps, -1).roll(shifts=1, dims=1)

        if self.cfg.env.relative_to_root:            
            # Set the first step to current state if relative to root 
            rolled_ref_root_rot[:, 0] = self.base_quat.clone()
            rolled_ref_root_vel[:, 0] = self.root_states[:, 7:10].clone()
            rolled_ref_root_ang_vel[:, 0] = self.root_states[:, 10:13].clone()
            rolled_ref_kb_pos[:, 0] = self.rigid_body_states[:, self.key_body_indices, :3].clone()
            rolled_ref_kb_vel[:, 0] = self.rigid_body_states[:, self.key_body_indices, 7:10].clone()
            rolled_ref_dof_pos[:, 0] = self.dof_pos.clone()
        else:
            rolled_ref_root_rot[:, 0] = self.cur_ref_root_rot.clone()
            rolled_ref_root_vel[:, 0] = self.cur_ref_root_vel.clone()
            rolled_ref_root_ang_vel[:, 0] = self.cur_ref_root_ang_vel.clone()
            rolled_ref_kb_pos[:, 0] = transform_local_to_global(self.cur_local_kb_pos.clone(), self.cur_ref_root_pos.clone(),
                                                                 self.cur_ref_root_rot.clone())
            rolled_ref_kb_vel[:, 0] = self.cur_ref_kb_vel.clone()
            rolled_ref_dof_pos[:, 0] = self.cur_ref_dof_pos.clone()

        rolled_ref_root_rot_flat = rolled_ref_root_rot.reshape(-1, 4)
        rolled_ref_root_vel_flat = rolled_ref_root_vel.reshape(-1, 3)
        rolled_ref_root_ang_vel_flat = rolled_ref_root_ang_vel.reshape(-1, 3)
        rolled_ref_kb_pos_flat = rolled_ref_kb_pos.reshape(-1, len(self.key_body_indices), 3)
        rolled_ref_kb_vel_flat = rolled_ref_kb_vel.reshape(-1, len(self.key_body_indices), 3)
        rolled_ref_dof_pos_flat = rolled_ref_dof_pos.reshape(-1, self.num_dof)

        # Calculate facing vectors and gravity projections
        rolled_root_facing_vec = quat_rotate(rolled_ref_root_rot_flat, self.forward_vec.repeat(self.num_future_steps, 1))
        target_delta_facing_vec = (next_root_facing_vec - rolled_root_facing_vec).reshape(self.num_envs, self.num_future_steps, 3)

        rolled_root_proj_grav = quat_rotate_inverse(rolled_ref_root_rot_flat, self.gravity_vec.repeat(self.num_future_steps, 1))
        target_delta_proj_grav = (next_root_proj_grav - rolled_root_proj_grav).reshape(self.num_envs, self.num_future_steps, 3)
        
        # Calculate local velocities
        target_local_root_vel = quat_rotate_inverse(rolled_ref_root_rot_flat, 
                                                    next_ref_root_vel - rolled_ref_root_vel_flat).reshape(self.num_envs, self.num_future_steps, 3)
        
        target_local_root_ang_vel = quat_rotate_inverse(rolled_ref_root_rot_flat,
                                                        next_ref_root_ang_vel - rolled_ref_root_ang_vel_flat).reshape(self.num_envs, self.num_future_steps, 3)
        
        # Calculate local keypoint positions and velocities
        rolled_ref_root_rot_expand = rolled_ref_root_rot_flat.unsqueeze(-2).repeat(1, len(self.key_body_indices), 1)
        next_ref_kb_pos_flat = next_ref_kb_pos.reshape(-1, len(self.ref_kb_indices), 3)
        
        target_local_kb_pos = quat_rotate_inverse(rolled_ref_root_rot_expand.view(-1, 4), 
                                                (next_ref_kb_pos_flat - rolled_ref_kb_pos_flat).view(-1, 3)).reshape(self.num_envs, self.num_future_steps, -1)
        # mask the trainsition frame with large global postion change.
        max_target_local_kb_diff = target_local_kb_pos.abs().max(-1, keepdim=False)[0]
        large_diff_mask = (max_target_local_kb_diff > 0.1).unsqueeze(-1).expand_as(target_local_kb_pos)
        target_local_kb_pos = torch.where(large_diff_mask, torch.zeros_like(target_local_kb_pos), target_local_kb_pos)

        next_ref_kb_vel_flat = next_ref_kb_vel.reshape(-1, len(self.ref_kb_indices), 3)
        target_local_kb_vel = quat_rotate_inverse(rolled_ref_root_rot_expand.view(-1, 4),
                                                (next_ref_kb_vel_flat - rolled_ref_kb_vel_flat).view(-1, 3)).reshape(self.num_envs, self.num_future_steps, -1)
        
        # Calculate DOF position difference
        target_dof_pos = (next_ref_dof_pos - rolled_ref_dof_pos_flat).reshape(self.num_envs, self.num_future_steps, self.num_dof)

        obs_to_return = (
            next_ref_root_height.view(self.num_envs, self.num_future_steps, 1),
            target_delta_proj_grav,
            target_delta_facing_vec,
            target_local_root_vel,
            target_local_root_ang_vel,
            target_dof_pos,
            target_local_kb_pos,
            target_local_kb_vel
        )
        return obs_to_return

    def update_ref_motion_obs(self):
        next_ref_motion = self.motion_lib.get_motion_state(self.next_ref_motion_ids.view(-1), self.next_ref_motion_times.view(-1))
        (
            next_ref_root_height,
            target_delta_proj_grav,
            target_delta_facing_vec,
            target_local_root_vel,
            target_local_root_ang_vel,
            target_dof_pos,
            target_local_kb_pos,
            target_local_kb_vel
        ) = self.extract_ref_motion_obs(next_ref_motion)

        target_task_obs = torch.cat([
            next_ref_root_height,
            target_delta_proj_grav,
            target_delta_facing_vec,
            target_local_root_vel * self.obs_scales.lin_vel,
            target_local_root_ang_vel * self.obs_scales.ang_vel,
            target_dof_pos * self.obs_scales.dof_pos,
            target_local_kb_pos,
            target_local_kb_vel,
        ], dim=-1).view(self.num_envs, -1)
        self.task_observation_buf[:] = target_task_obs        

        # update the cur ref motion for reward and metrics calculation
        self.update_cur_ref_motion_obs()

        self.ang_z_vel_cmd[:] = target_local_root_ang_vel[:, 0, 2] * self.obs_scales.ang_vel
        # extract root command from the ref motion
        self.root_commands[:, 0:2] = target_local_root_vel[:, 0, 0:2]
        self.root_commands[:, 0] *= torch.abs(self.root_commands[..., 0]) > self.cfg.commands.lin_vel_clip
        self.root_commands[:, 1] *= torch.abs(self.root_commands[..., 1]) > self.cfg.commands.lin_vel_clip
        self.root_commands[:, 2] = target_local_root_ang_vel[:, 0, 2]
        self.root_commands[:, 2] *= torch.abs(self.root_commands[..., 2]) > self.cfg.commands.ang_vel_clip
        self.root_commands[:, 3] = next_ref_root_height.view(self.num_envs, self.num_future_steps, 1)[:, 0, 0]

    def check_termination(self):
        # base termination condition
        self.reset_buf = torch.any(torch.norm(self.contact_forces[:, self.termination_contact_indices, :], dim=-1) > 5., dim=1)
        time_out_threshold = torch.maximum(self.motion_lib.get_motion_tick_spans(self.motion_ids), 
                                           torch.ones_like(self.motion_lib.get_motion_tick_spans(self.motion_ids)) * self.cfg.env.episode_length_s)
        if self.cfg.motion_lib.cyclic_transition:
            self.time_out_buf = self.episode_length_buf * self.dt > time_out_threshold
        else:
            self.time_out_buf = self.motion_times > time_out_threshold

        # relaxed termination condition
        height_cutoff = self.root_states[:, 2] < self.cfg.rewards.termination_height
        roll_cut = torch.abs(self.roll) > 1.0
        pitch_cut = torch.abs(self.pitch) > 1.0

        # reference-based early termination
        cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3]
        global_root_trans = self.target_global_root_trans[:].clone()
        global_root_pos = torch.cat([global_root_trans, self.cur_ref_root_height], dim=-1)
        cur_ref_kb_pos = transform_local_to_global(self.cur_local_kb_pos, global_root_pos, self.cur_ref_root_rot)
        key_bodies_pos_diff = (cur_ref_kb_pos - cur_robot_kb_pos).pow(2).sum(-1).sqrt()  # (n_envs, n_kb)
        key_bodies_pos_diff[:, self.non_terminate_kb_indices] = 0.0
        max_key_body_pos_err = key_bodies_pos_diff.max(-1)[0]
        track_termination_dist = self.cur_termination_dist[:].clone()
        max_kb_pos_err_cut = max_key_body_pos_err > track_termination_dist
        self.reset_buf |= self.time_out_buf
        self.reset_buf |= (height_cutoff * self.cfg.env.enable_height_termination)
        self.reset_buf |= (roll_cut * self.cfg.env.enable_orient_termination)
        self.reset_buf |= (pitch_cut * self.cfg.env.enable_orient_termination)
        # add the ET diff curriculum
        self.reset_buf |= (max_kb_pos_err_cut * self.cfg.env.enable_max_kb_pos_err_cut)

    def get_walking_cmd_mask(self, env_ids=None, return_all=False):
        if env_ids is None:
            env_ids = torch.arange(self.num_envs, device=self.device)
        walking_mask0 = torch.abs(self.cur_ref_root_vel[env_ids, 0]) > self.cfg.commands.lin_vel_clip
        walking_mask1 = torch.abs(self.cur_ref_root_vel[env_ids, 1]) > self.cfg.commands.lin_vel_clip
        ang_z_vel_cmd = self.ang_z_vel_cmd.clone()
        walking_mask2 = torch.abs(ang_z_vel_cmd)[env_ids] > self.cfg.commands.ang_vel_clip
        walking_mask = walking_mask0 | walking_mask1 | walking_mask2
        if return_all:
            return walking_mask0, walking_mask1, walking_mask2, walking_mask
        return walking_mask

    def draw_key_bodies_demo(self):
        keypoint_geom = gymutil.WireframeSphereGeometry(0.06, 32, 32, None, color=(0, 1, 0))
        cur_local_kb_pos = self.cur_local_kb_pos.clone()
        if self.cfg.motion_lib.global_tracking:
            global_root_trans = self.target_global_root_trans[:].clone()
        else:
            # exclude the root translation from the key body tracking
            global_root_trans = self.root_states[:, :2].clone()
        global_root_pos = torch.cat([global_root_trans, self.cur_ref_root_height], dim=-1)
        global_root_rot = self.cur_ref_root_rot[:].clone()
        cur_ref_kb_pos = transform_local_to_global(cur_local_kb_pos, global_root_pos, global_root_rot)

        for i in range(len(self.ref_kb_indices)):
            pose = gymapi.Transform(
                gymapi.Vec3(cur_ref_kb_pos[self.lookat_id, i, 0], cur_ref_kb_pos[self.lookat_id, i, 1],
                            cur_ref_kb_pos[self.lookat_id, i, 2]), r=None)
            gymutil.draw_lines(keypoint_geom, self.gym, self.viewer, self.envs[self.lookat_id], pose)

    def _motion_sync(self):
        env_ids = torch.arange(self.num_envs, dtype=torch.long, device=self.device)
        cur_ref_motion = self.motion_lib.get_motion_state(self.motion_ids, self.motion_times)

        root_pos = cur_ref_motion.rb_pos[:, 0].clone()
        root_pos[:, :2] += self.init_ref_root_offset
        root_rot = cur_ref_motion.rb_rot[:, 0].clone()
        root_vel = torch.zeros_like(root_pos)
        root_ang_vel = torch.zeros_like(root_pos)

        dof_pos = cur_ref_motion.dof_pos[:, self.ref_dof_indices].clone()
        dof_vel = torch.zeros_like(dof_pos)
        self._set_env_state(env_ids=env_ids,
                            root_pos=root_pos,
                            root_rot=root_rot,
                            dof_pos=dof_pos,
                            root_vel=root_vel,
                            root_ang_vel=root_ang_vel,
                            dof_vel=dof_vel)

        env_ids_int32 = env_ids.to(dtype=torch.int32)
        self.gym.set_actor_root_state_tensor_indexed(self.sim,
                                                     gymtorch.unwrap_tensor(self.root_states),
                                                     gymtorch.unwrap_tensor(env_ids_int32), len(env_ids_int32))
        self.gym.set_dof_state_tensor_indexed(self.sim,
                                              gymtorch.unwrap_tensor(self.dof_state),
                                              gymtorch.unwrap_tensor(env_ids_int32), len(env_ids_int32))
        return
    
    def compute_reward(self):
        self.rew_buf[:] = 0.
        for i in range(len(self.reward_functions)):
            name = self.reward_names[i]
            rew = self.reward_functions[i]() 
            if name in self.cfg.rewards.regularization_names:
                self.rew_buf += rew * self.reward_scales[name] * self.cfg.rewards.regularization_scale
            else: 
                self.rew_buf += rew * self.reward_scales[name] * self.cfg.rewards.reward_multiplier
            self.episode_sums[name] += rew
        if self.cfg.rewards.only_positive_rewards:
            self.rew_buf[:] = torch.clip(self.rew_buf[:], min=0.)
        if self.cfg.rewards.clip_rewards:
            self.rew_buf[:] = torch.clip(self.rew_buf[:], min=-0.5)
        
        # add termination reward after clipping
        if "termination" in self.reward_scales:
            rew = self._reward_termination() * self.reward_scales["termination"]
            self.rew_buf += rew
            self.episode_sums["termination"] += rew
        
        self.calculate_tracking_metrics()
    
    def calculate_tracking_metrics(self):
        # calculate extra tracking metrics for policy evalaution
        # gmpjpe and max_gmpjpe 
        cur_local_kb_pos = self.cur_local_kb_pos.clone()
        target_global_root_pos = torch.cat([self.target_global_root_trans, self.cur_ref_root_height], dim=-1)
        global_root_rot = self.cur_ref_root_rot[:].clone()
        cur_ref_kb_pos = transform_local_to_global(cur_local_kb_pos, target_global_root_pos, global_root_rot)
        cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3].clone()
        global_kb_pos_diff = (cur_ref_kb_pos - cur_robot_kb_pos).pow(2).sum(-1).sqrt()
        global_kb_pos_err = global_kb_pos_diff.mean(-1)
        max_global_kb_pos_err = global_kb_pos_diff.max(-1)[0]
        self.cum_tracking_errors["gmpjpe"] += global_kb_pos_err
        self.cum_tracking_errors["max_gmpjpe"] += max_global_kb_pos_err
        self.tracking_errors["gmpjpe"] = global_kb_pos_err
        self.tracking_errors["max_gmpjpe"] = max_global_kb_pos_err
        # mpjpe
        local_kb_pos_diff = (cur_local_kb_pos - self.robot_local_kb_pos).pow(2).sum(-1).sqrt()
        local_kb_pos_err = local_kb_pos_diff.mean(-1)
        self.cum_tracking_errors["mpjpe"] += local_kb_pos_err
        self.tracking_errors["mpjpe"] = local_kb_pos_err

    # ======================================================================================================================
    # Reward functions
    # ======================================================================================================================
    def _reward_tracking_ref_yaw(self):
        r = torch.exp(-torch.abs(self.target_yaw - self.yaw))
        return r

    def _reward_tracking_ref_roll_pitch(self):
        cur_ref_roll, cur_ref_pitch, _ = euler_from_quaternion(self.cur_ref_root_rot.clone())
        cur_ref_roll_pitch = torch.stack((cur_ref_roll, cur_ref_pitch), dim=1)
        cur_robot_roll_pitch = torch.stack((self.roll, self.pitch), dim=1)
        r = torch.exp(-torch.norm(cur_robot_roll_pitch - cur_ref_roll_pitch, dim=1))
        return r

    def _reward_tracking_ref_dof_pos(self):
        joint_pos = self.dof_pos.clone()
        ref_dof_pos = self.cur_ref_dof_pos.clone()
        diff = (joint_pos - ref_dof_pos)
        joint_weights = torch.tensor(self.cfg.rewards.joint_weights, device=self.device)
        diff = diff * joint_weights[None, :]
        r = torch.exp(-1.0 * torch.norm(diff, dim=1)) - 0.2 * torch.norm(diff, dim=1).clamp(0, 0.5)
        return r
    
    def _reward_tracking_ref_dof_vel(self):
        joint_vel = self.dof_vel.clone()
        ref_dof_vel = self.cur_ref_dof_vel.clone()
        diff = joint_vel - ref_dof_vel
        joint_weights = torch.tensor(self.cfg.rewards.joint_weights, device=self.device)
        diff = diff * joint_weights[None, :]
        r = torch.exp(-0.05 * torch.norm(diff, dim=1))
        return r

    def _reward_tracking_ref_kb_pos(self):
        cur_local_kb_pos = self.cur_local_kb_pos.clone()
        target_global_root_pos = torch.cat([self.target_global_root_trans, self.cur_ref_root_height], dim=-1)
        global_root_rot = self.cur_ref_root_rot[:].clone()
        cur_ref_kb_pos = transform_local_to_global(cur_local_kb_pos, target_global_root_pos, global_root_rot)
        cur_robot_kb_pos = self.rigid_body_states[:, self.key_body_indices, :3]
        diff = cur_ref_kb_pos - cur_robot_kb_pos
        keybody_weights = torch.tensor(self.cfg.rewards.keybody_weights, device=self.device)
        diff = (diff * keybody_weights[None, :, None]).view(self.num_envs, -1)
        r = torch.exp(-1.0 * torch.norm(diff, dim=1))
        return r
    
    def _reward_tracking_ref_height(self):
        cur_ref_root_height = self.cur_ref_root_height.clone()
        cur_robot_root_height = self.root_states[:, 2:3]
        diff = cur_ref_root_height - cur_robot_root_height
        r = torch.exp(-40.0 * torch.norm(diff, dim=1))
        return r
    
    def _reward_tracking_ref_kb_rot(self):
        cur_ref_kb_rot = self.cur_ref_kb_rot.clone()
        cur_robot_kb_rot = self.rigid_body_states[:, self.key_body_indices, 3:7]
        diff = quat_diff_norm(cur_ref_kb_rot, cur_robot_kb_rot)
        keybody_weights = torch.tensor(self.cfg.rewards.keybody_weights, device=self.device)
        diff = diff * keybody_weights[None, :]
        r = torch.exp(-2.0 * torch.mean(diff, dim=-1))
        return r

    def _reward_tracking_ref_lin_vel(self):
        cur_ref_root_vel = self.cur_ref_root_vel.clone()
        cur_robot_root_vel = self.root_states[:, 7:10].clone()
        diff = cur_ref_root_vel - cur_robot_root_vel
        r = torch.exp(-1.0 * torch.norm(diff, dim=1))
        return r

    def _reward_tracking_ref_kb_lin_vel(self):
        cur_ref_kb_vel = self.cur_ref_kb_vel.clone()
        cur_robot_kb_vel = self.rigid_body_states[:, self.key_body_indices, 7:10]
        diff = (cur_ref_kb_vel - cur_robot_kb_vel).view(self.num_envs, -1)
        r = torch.exp(-0.1 * torch.norm(diff, dim=1))
        return r

    def _reward_tracking_ref_ang_vel(self):
        cur_ref_root_ang_vel = self.cur_ref_root_ang_vel.clone()
        cur_local_root_ang_vel = quat_rotate_inverse(self.cur_ref_root_rot.clone(), cur_ref_root_ang_vel)
        diff = cur_local_root_ang_vel - self.base_ang_vel
        ang_vel_error = torch.norm(diff, dim=1)
        r = torch.exp(-0.5 * ang_vel_error)
        return r
    
    def _reward_tracking_ref_kb_ang_vel(self):
        cur_ref_kb_ang_vel = self.cur_ref_kb_ang_vel.clone()
        cur_robot_kb_ang_vel = self.rigid_body_states[:, self.key_body_indices, 10:13]
        diff = (cur_ref_kb_ang_vel - cur_robot_kb_ang_vel).view(self.num_envs, -1)
        r = torch.exp(-0.02 * torch.norm(diff, dim=1))
        return r

    def _reward_dof_error_waist(self):
        dof_error = torch.sum(torch.square(self.dof_pos - self.default_dof_pos)
                              [:, self.waist_indices], dim=1)
        return dof_error
    
    def _reward_feet_orientation(self):
        hip_yaw_indices = self.knee_indices - 1
        dof_error = torch.sum(torch.square(self.dof_pos - self.default_dof_pos)[:, hip_yaw_indices], dim=1)
        return dof_error * (self.root_commands[:, 2] == 0).float()
    
    def _reward_dof_vel(self):
        return torch.sum(torch.square(self.dof_vel), dim=1)
    
    def _reward_tracking_feet_contacts(self):
        cur_ref_feet_contacts = self.cur_ref_contact_profile.clone()
        ref_left_foot_contact_signal = cur_ref_feet_contacts[:, 0]
        ref_right_foot_contact_signal = cur_ref_feet_contacts[:, 2]  # considering the ref heel contact
        cur_ref_feet_contact_signal = torch.stack((ref_left_foot_contact_signal, ref_right_foot_contact_signal), dim=1)
        
        heel_contact = self.contact_forces[:, self.feet_indices, 2] > 5.
        toe_contact = self.contact_forces[:, self.feet_indices+1, 2] > 5.
        contact = torch.logical_or(heel_contact, toe_contact)
        r = torch.where(cur_ref_feet_contact_signal == contact, 1, -0.3)
        return torch.mean(r, dim=-1)
    
    def _reward_no_fly(self):
        contact = self.contact_forces[:, self.feet_indices, 2] < 5.
        flying_mask = torch.logical_and(contact[:, 0], contact[:, 1])
        return torch.where(flying_mask, 1.0, 0.0).float()
    
    # def _reward_flying_phase(self):
    #     cur_ref_feet_contacts = self.cur_ref_contact_profile.clone()
    #     ref_flying_phase = torch.all(cur_ref_feet_contacts == 0, dim=-1)

    #     heel_swing = (self.contact_forces[:, self.feet_indices, 2] < 1.).all(dim=-1)
    #     toe_swing = (self.contact_forces[:, self.feet_indices+1, 2] < 1.).all(dim=-1)
    #     flying_states = torch.logical_and(heel_swing, toe_swing)
    #     r = torch.where(flying_states == ref_flying_phase, 0, -10)
    #     return r


@torch.jit.script
def transform_global_to_local(global_kb_pos: Tensor, global_root_pos: Tensor, root_rot: Tensor, heading_only:bool = False):
    """
    global_kb_pos: The global position of the key body with an extra body dim.
    global_root_pos: The global root position.
    root_rot: The global root rot quat.
    """
    batch, num_key_bodies = global_kb_pos.shape[:2]
    local_kb_pos = global_kb_pos - global_root_pos.unsqueeze(1)  # expand the body dim
    if heading_only:
        root_rot = calc_heading_quat(root_rot)
    head_rot_expand = root_rot.unsqueeze(1).expand(-1, num_key_bodies, -1)
    local_kb_pos = quat_rotate_inverse(head_rot_expand.reshape(-1, 4), local_kb_pos.view(-1, 3)).view(global_kb_pos.shape)
    return local_kb_pos

@torch.jit.script
def transform_local_to_global(local_kb_pos: Tensor, global_root_pos: Tensor, root_rot: Tensor, heading_only:bool = False):
    """
    local_kb_pos: The local position of the key body with an extra body dim.
    global_root_pos: The target global root position.
    root_rot: The target global root rot quat.
    """
    batch, num_key_bodies = local_kb_pos.shape[:2]
    if heading_only:
        root_rot = calc_heading_quat(root_rot)
    heading_rot_expand = root_rot.unsqueeze(1).expand(-1, num_key_bodies, -1)
    global_body_pos = quat_rotate(heading_rot_expand.reshape(-1, 4), local_kb_pos.view(-1, 3)).view(local_kb_pos.shape)
    global_body_pos += global_root_pos[:, None]
    return global_body_pos
